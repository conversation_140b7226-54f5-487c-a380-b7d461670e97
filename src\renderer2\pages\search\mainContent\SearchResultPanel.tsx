import { keyDown, keyEnter, keyUp, MinSearchDataLen, useGlobalStore, dataOpticsApi1, referenceProductItem, userRole, useBuyerSettingStore, getFormattedUnit } from '@bryzos/giss-ui-library';
import React, { useEffect, useRef, useState } from 'react'
import ProductDescription from '../../../component/ProductDescription/ProductDescription';
import { useSearchStore } from '../../../store/SearchStore';

import styles from '../home.module.scss'
import clsx from 'clsx';
import { HttpRequestPayload, ProductPricingModel, SearchAnalyticDataModel } from 'src/renderer2/types/Search'
import { v4 as uuidv4 } from 'uuid';
import { ReferenceDataProduct } from 'src/renderer2/types/ReferenceDataProduct';
import { getFormattedProductPricingForSearchSelection } from 'src/renderer2/helper';
import { getPriceExample } from 'src/renderer2/utility/priceIntegratorExample';

const SearchResultPanel: React.FC = () => {
    const { productData }: any = useGlobalStore();
    const { searchByProductResult, filterSearchByProductResult, searchString } = useSearchStore();
    const containerRef = useRef<HTMLDivElement>(null);
    const selectedProductRef = useRef(null);
    const [selectedIndex, setSelectedIndex] = useState<number>(-1);
    const { sessionId, selectedDomesticOption, shortListedSearchProductsData, selectedPriceUnit,  setEnableRejectSearchAnalytic, setSearchByProductResult, setSearchString, setShortListedSearchProductsData,setFilterShortListedSearchProductsData } = useSearchStore();


    const selectProductFromSearch = (product: ReferenceDataProduct) => {
        setEnableRejectSearchAnalytic(false)
        selectProduct(product, sessionId);
        setSearchByProductResult([]);
        setSearchString('');
    }
    
    const selectProduct = async (
        product: ReferenceDataProduct,
        sessionId: string
    ) => {
        // 
        const isDuplicate = shortListedSearchProductsData.some((data) => data.id === product.Product_ID);
        if (!isDuplicate) {
            const store: any = useGlobalStore.getState();
            const { userData } = store;
            const { searchZipCode, orderSizeSliderValue } = useSearchStore.getState();
            const { buyerSetting } = useBuyerSettingStore.getState();
            const defaultZipCode = buyerSetting?.price_search_zip || '63105';
            const _zipcode = searchZipCode?.length === 5 ? searchZipCode :  defaultZipCode;
            const spreadProduct: any = {...product};
            if (!product.is_safe_product_code) {
                const newPrices = await getPriceExample(product.Product_ID, _zipcode, Math.floor(orderSizeSliderValue));
                if (newPrices) {
                    spreadProduct.cwt_price = newPrices.cwt;
                    spreadProduct.lb_price = newPrices.lb;
                    spreadProduct.ft_price = newPrices.ft;
                    spreadProduct.pc_price = newPrices.pc;
                }
            }else{
                spreadProduct.cwt_price = Number(product?.Neutral_Pricing_CWT);
                spreadProduct.lb_price = Number(product?.Neutral_Pricing_LB);
                spreadProduct.ft_price = Number(product?.Neutral_Pricing_Ft);
                spreadProduct.pc_price = Number(product?.Neutral_Pricing_Ea);
            }
            const productWithPrice = getFormattedProductPricingForSearchSelection(spreadProduct);
            const _selectedProducts = [productWithPrice, ...shortListedSearchProductsData];
            setShortListedSearchProductsData(_selectedProducts);

            let filteredSelectedProductSearchData = [..._selectedProducts];
            if (selectedDomesticOption) {
                filteredSelectedProductSearchData = _selectedProducts.filter((selectedProduct) => selectedProduct.domestic_material_only);
            }
            setFilterShortListedSearchProductsData(filteredSelectedProductSearchData);

            const dataOpticsPayload: HttpRequestPayload<SearchAnalyticDataModel[]> = {
                "data": [{
                    "session_id": sessionId,
                    "line_session_id": productWithPrice.line_session_id,
                    "product_id": productWithPrice.id,
                    "description": productWithPrice.UI_Description,
                    "price_shared": false,
                    "price_share_unit": getFormattedUnit(selectedPriceUnit)?.toLowerCase(),
                    "search_price_unit": getFormattedUnit(selectedPriceUnit)?.toLowerCase(),
                    "zip_code": _zipcode.trim(),
                    "order_size": String(orderSizeSliderValue),
                    "price" : {
                        "price_ft": productWithPrice?.ft_price,
                        "price_lb": productWithPrice?.lb_price,
                        "price_cwt": productWithPrice?.cwt_price,
                        "price_pc": productWithPrice?.pc_price,
                    },
                }]
            }
            dataOpticsApi1(dataOpticsPayload);
        }
    }

    useEffect(() => {
        const handleSearchKeyDown = (event: KeyboardEvent) => {
            const startIndex = 0;
            const endIndex = searchByProductResult.length - 1;
            if (event.key === keyUp && selectedIndex > startIndex) {
                setSelectedIndex(selectedIndex - 1)
            } else if (event.key === keyDown && selectedIndex < endIndex) {
                setSelectedIndex(selectedIndex + 1)
            } else if ((event.key === keyEnter || event.key === 'Tab') && selectedIndex >= startIndex) {
                setSelectedIndex(-1);
                const element: any = selectedProductRef.current;
                const selectedProductId = element.getAttribute('data-value');
                selectProductFromSearch(productData[selectedProductId - 1]);
            }
        }
        document.addEventListener('keydown', handleSearchKeyDown);
        return () => {
            document.removeEventListener('keydown', handleSearchKeyDown);
        };
    }, [selectedIndex, searchByProductResult])

    useEffect(() => {
        const container = containerRef.current;
        const selectedItem: any = selectedProductRef.current;
        if (container && selectedProductRef && selectedItem) {
            const itemTop = selectedItem.offsetTop - 140;
            const itemHeight = selectedItem.offsetHeight + 40;
            const containerHeight = container.offsetHeight;
            const scrollOffset = container.scrollTop;
            if (itemTop < scrollOffset) {
                container.scrollTop = itemTop - 50;
            } else if (itemTop + itemHeight > scrollOffset + containerHeight) {
                container.scrollTop = itemTop + itemHeight - containerHeight - 90;
            }
        }
    }, [selectedIndex]);


    useEffect(() => {
        animateProductSearchContainer();
    }, [filterSearchByProductResult])

    const animateProductSearchContainer = () => {
        const container = document.getElementById('search-container');
        const padding = 16;
        if (container) {
            if (searchString.length < MinSearchDataLen) {
                container.style.maxHeight = '0px';
                return;
            }
            let result;
            if (filterSearchByProductResult.length > 0) {
                result = document.getElementById('search-result');
            }
            else {
                result = document.getElementById('no-result-search');
            }
            if (result) {
                const resultHeight = Number(result.offsetHeight);
                container.style.maxHeight = resultHeight > 542 ? `${542}px` : `${resultHeight + padding}px`;
            }
        }
    }

    return (
        <>
            {searchString.length > 1 &&
                <div className={clsx(styles.searchProductDescMain, ( selectedProductRef ) &&  styles.padding0)} ref={containerRef} id='search-container'>
                    {searchString.length > 0 && <div id='search-result' className={styles.searchResult}>
                        {(filterSearchByProductResult.length > 0) ?
                            filterSearchByProductResult.map((product: ReferenceDataProduct, index: number) => (
                                <div key={product.id} className={clsx(styles.searchProductDescription, index === selectedIndex && styles.highlightSearchProductDescription)}
                                    ref={index === selectedIndex ? selectedProductRef : null}
                                    data-value={product.id}
                                    onClick={() => selectProductFromSearch(product)}>
                                    <ProductDescription product={product} />
                                </div>
                            ))
                            :
                            <div className={styles.NoResultsToDisplayText1} id="no-result-search">
                                <p className={styles.marginBottom10}>NO RESULTS TO DISPLAY</p>
                            </div>
                        }
                    </div>}
                </div>
            }
        </>
    )
}

export default SearchResultPanel