.tncBox {
    width: 100%;
    background: url(../../assets/New-images/AppBG.svg) no-repeat;
    // border-image-source: linear-gradient(to bottom, #fff -13%, #1a1b21 28%);
    // border-image-slice: 1;
    // background-image: linear-gradient(351deg, #2b2d33 238%, #0f0f14 -121%), linear-gradient(to bottom, #fff -13%, #1a1b21 28%);
    // background-origin: border-box;
    // background-clip: content-box, border-box;

    .tnCInnerContent {
        padding: 23.1px 39.5px 31.6px 39.5px;

        .tncBoxContent {
            width: 100%;
            padding: 21.1px 10.5px 21.1px 39.5px;
            border-radius: 21.1px;
            background-color: rgba(255, 255, 255, 0.04);
            height: 619.8px;

            .tnCPage {
                    overflow: auto;
                    height: 100%;
                    padding-right: 22px;
            
                    &::-webkit-scrollbar {
                        width: 8px;
                        height: 6px;
                    }
            
                    &::-webkit-scrollbar-thumb {
                        background:
                            #9da2b2;
                        border-radius: 50px;
                    }
            
                    &::-webkit-scrollbar-track {
                        background: transparent;
                    }
                }

          

            .tncScrollClass {
                .TermsofUseV1 {
                    font-family: Inter;
                    font-size: 31.6px;
                    font-weight: bold;
                    line-height: 1.35;
                    letter-spacing: normal;
                    text-align: left;
                    color: #fff;
                    margin-bottom: 15.8px;
                }
            }
        }
    }

    .btnFooterTnc {
        width: 100%;
        height: 120px;
        padding: 0 40px 0 54px;
        box-shadow: 0 5.5px 4.4px -3px rgba(0, 0, 0, 0.91);
        background: url(../../assets/New-images/Create-Account/footerCreateAccount.svg) no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        justify-content: space-between;

        .btnBack {
            button {
                font-family: Inter;
                font-size: 16px;
                font-weight: normal;
                font-style: normal;
                line-height: 1;
                letter-spacing: normal;
                text-align: left;
                color: rgba(255, 255, 255, 0.4);
                display: flex;
                align-items: center;
                svg {
                    margin-right: 10px;
                }
                span{
                    padding-top: 4px;
                }
            }
        }

        .alreadyAccountLogin {
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.4);
            margin-left: auto;
            margin-left: 64px;
            span{
                color: #fff;
                cursor: pointer;
              } 
          }

          .nextTncBtn{
            width: 160px;
            height: 52px;
            display: flex;
            flex-direction: row;
            justify-content:center;
            align-items: center;
            border-radius: 12px;
            box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
            background: linear-gradient(236.5deg, rgba(255, 119, 89, 0.1) 0%, rgba(255, 83, 45, 0.1) 100%);
            &[disabled]{
                opacity: unset;
                background: rgba(255, 255, 255, 0.04);
                box-shadow: none;
                border-radius: 20px;
                span{
                   background-image: unset;
                    color: rgba(255, 255, 255, 0.4);
                    -webkit-text-fill-color:unset
                }
           }
            span{
                background-image: linear-gradient(316deg, #ff7759 130%, #ff532d -17%);
                font-family: Syncopate;
                font-size: 18.5px;
                font-weight: bold;
                font-stretch: normal;
                line-height: 1.3;
                letter-spacing: -0.74px;
                text-align: left;
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                text-transform: uppercase;
            }
          }
          
    }
}


.ErrorDialog {
    .dialogContent {
        max-width: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 30px 34px 30px 34px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 20px;
        }


        .submitBtn {
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 10px 24px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            background-color: transparent;
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: center;
            color: #fff;
            margin-top: 20px;
            transition: all 0.1s;

            &:hover {
                background-color: #70ff00;
                border: solid 0.5px #70ff00;
                color: #000;
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    border: solid 0.5px #fff;
                    background-color: transparent;
                    color: #fff;
                }
            }
        }


    }

}

.checkingThisbox {
    margin-top: 40.8px;
    display: flex;
    align-items: flex-start;
    column-gap: 21.1px;
    padding-left: 39.5px;
    padding-right: 8px;

    .containerChk {
        display: inline-block;
        position: relative;
        cursor: pointer;
        padding-left: 20px;
        text-align: left;

        .lblChk {
            font-family: Noto Sans;
            font-size: 12px;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            text-align: left;
            padding-right: 10px;
        }

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .checkmark {
            position: absolute;
            top: 2px;
            left: 0;
            z-index: 1;
            width: 21.6px;
            height: 21.6px;
            background: url(../../assets/New-images/Create-Account/uncheckmark.svg) no-repeat;
            background-size: contain;
           
        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        input:checked~.checkmark {
            background: url(../../assets/New-images/Create-Account/checkmark.svg) no-repeat;
            background-size: contain;
        }

        input:checked~.checkmark:after {
            display: block;
        }

    }

    .lblChk {
        font-family: Inter;
        font-size: 18.4px;
        font-weight: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.6);
    }
}