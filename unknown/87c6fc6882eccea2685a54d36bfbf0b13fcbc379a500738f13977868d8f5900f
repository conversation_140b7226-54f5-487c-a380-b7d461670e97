import React, { useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import CustomToggleCheckbox from 'src/renderer2/component/CustomToggleCheckbox';
import clsx from 'clsx';
import { shipmentsSchema } from '../schemas';
import { BReceivingHoursTooltip, deleteCertificateLineTooltip, deleteCertificateTooltip } from 'src/renderer2/tooltip';
import { Dialog, Fade, Tooltip, Autocomplete, TextField } from '@mui/material';
import { CustomMenu } from '../../CustomMenu';
import axios from 'axios';
import { buyerSettingConst, useGlobalStore } from '@bryzos/giss-ui-library';
import { commomKeys, defaultResaleCertificateLine, prefixUrl, reactQueryKeys, RecevingHoursFrom, RecevingHoursTo } from 'src/renderer2/common';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { ReactComponent as DeleteIcon } from '../../../../assets/images/delete.svg';
import { ReactComponent as AddCertiIcon } from '../../../../assets/images/AddLine.svg';
import { ReactComponent as DeleteCertiIcon } from '../../../../assets/images/Remove-line.svg';
import ResaleCertificateLineComponent from '../components/ResaleCertificateLineComponent';
import CustomAddressComponent from '../components/CustomAddressComponent';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';

import { v4 as uuidv4 } from 'uuid';
import { useQueryClient } from '@tanstack/react-query';
import { unformatPhoneNumber } from 'src/renderer2/helper';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';

interface InputFocusState {
  deliveryContactName: boolean;
  deliveryPhoneNumber: boolean;
  deliveryEmailAddress: boolean;
  shippingDocsEmail: boolean;
  addressNickName: boolean;
  deliveryFullAddress: boolean;
  deliveryAddressCity: boolean;
}

const MenuPropsTop = {
  classes: {
    paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown, styles.receivingHoursTop),
    list: styles.muiMenuList
  },
  anchorOrigin: {
    vertical: -5,
    horizontal: "left"
  },
  transformOrigin: {
    vertical: "bottom",
    horizontal: "left"
  },
}
const MenuPropsBottom = {
  classes: {
    paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown,styles.receivingHoursBottom),
    list: styles.muiMenuList
  },
  anchorOrigin: {
    vertical: 27,
    horizontal: "left"
  },
  transformOrigin: {
    vertical: "top",
    horizontal: "left"
  },
}

const ShipmentsTab: React.FC<{ mainWrapperRef: React.RefObject<HTMLDivElement>, register: any, handleSubmit: any, control: any, watch: any, setValue: any, getValues: any, setError: any, clearErrors: any, errors: any, buyerSettings: any, trigger: any , isDirty: any , deliveryAddresses: any, resetField: any, dirtyFields: any ,setDeliveryAddresses: any, setActiveTab: any}> = ({ mainWrapperRef, register, handleSubmit, control, watch, setValue, getValues, setError, clearErrors, errors, trigger , buyerSettings , trigger: any , isDirty , deliveryAddresses , setDeliveryAddresses , resetField, dirtyFields, setActiveTab   }) => {
  const { userData, showLoader, setShowLoader, referenceData }: any = useGlobalStore();
  const [States, setStates] = useState([]);
  const [ResaleExpiration, setResaleExpiration] = useState([]);
  const { showCommonDialog, resetDialogStore }: any = useDialogStore();
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [payloadData, setPayloadData] = useState('');
  const [deliveryAddressInput, setDeliveryAddressInput] = useState('');
  const [isAddressDropdownOpen, setIsAddressDropdownOpen] = useState(false);
  const [filteredCities, setFilteredCities] = useState<string[]>([]);
  const [isDeliveryAddressFocused, setIsDeliveryAddressFocused] = useState(false);
  const [isResaleCertificateFocused, setIsResaleCertificateFocused] = useState(false);
  const [customAddressComponentOpen, setCustomAddressComponentOpen] = useState(false);
  const [focusedInput, setFocusedInput] = useState<string | null>(null);
  const [validationInProgress, setValidationInProgress] = useState(true);
  const queryClient = useQueryClient();
  const [deliveryDropDownAddresses  , setDeliveryDropDownAddresses] = useState<any>([]);
  const [searchValue, setSearchValue] = useState('');
  const [filteredAddresses, setFilteredAddresses] = useState<any[]>([]);
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
const shipmentPopupRef = useRef(null);
const { mutate: saveUserSettings } = useSaveUserSettings();

  useEffect(() => {
    if (userData.data.id && referenceData) {
      setStates(referenceData.ref_states);
      let expiresData = [];
      referenceData?.ref_resale_cert_expiration.map((expiration) => {
        const expireData = {
          title: expiration.expiration_display_string,
          value: expiration.expiration_value
        }
        return expiresData = [...expiresData, expireData];
      })


      setResaleExpiration(expiresData);
    }
  }, [referenceData, userData]);


    useEffect(() => {
      if(watch('deliveryAddress') && watch('deliveryAddress') !== "" && watch('deliveryAddress.line1') && watch('deliveryAddress.city') && watch('deliveryAddress.stateCode') && watch('deliveryAddress.zip')){
        setSearchValue(watch('deliveryAddress.line1') + ", "  +  (watch('deliveryAddress.line2') && watch('deliveryAddress.line2') + ", " ) + watch('deliveryAddress.city') + ", " + watch('deliveryAddress.stateCode') + ", " + watch('deliveryAddress.zip'))
      }
    }, [watch('deliveryAddress')])
  useEffect(() => {
    setDeliveryDropDownAddresses(deliveryAddresses)
  }, [deliveryAddresses])

  useEffect(() => {
    // Initialize filtered addresses when deliveryAddresses changes
    if (deliveryAddresses && deliveryAddresses.length > 0) {
      setFilteredAddresses(deliveryAddresses);
    }
  }, [deliveryAddresses])

  useEffect(() => {
    const stateField = `newCompanyAddress.state`;
    const zipField = `newCompanyAddress.zip`;
    handleStateZipValidation(zipField, stateField);
  }, [watch('newCompanyAddress.zip'), watch('newCompanyAddress.state')])

  const deliveryApptRequired = watch('deliveryApptRequired');

  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    deliveryContactName: false,
    deliveryPhoneNumber: false,
    deliveryEmailAddress: false,
    shippingDocsEmail: false,
    deliveryFullAddress: false,
    deliveryAddressCity: false,
    addressNickName: false,
  });

  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
    if (dirtyFields[inputName]) {
      if (focusedInput !== 'newCompanyAddress') {
        setTimeout(() => {
          handleSaveShipmentSettings()
        }, 100)
      }
    }
  };

  const changeReceivingHrs = (dateIndex: any, isReceivingHrsFrom: any, dropdownValue: any) => {
    setValue(`dates.${dateIndex}.is_user_available`, true);
    const receivingHrsOption: any[] = [];
    let currentDropdown = `dates.${dateIndex}.to`;
    let adjacentDropdown = `dates.${dateIndex}.from`;
    let adjDropDownOptionsCopy = RecevingHoursFrom;
    let dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsFrom`;
    let onChangingCancelAdjDropDownValue = RecevingHoursFrom[0].value;
    if (isReceivingHrsFrom) {
      currentDropdown = `dates.${dateIndex}.from`;
      adjacentDropdown = `dates.${dateIndex}.to`;
      adjDropDownOptionsCopy = RecevingHoursTo;
      onChangingCancelAdjDropDownValue = RecevingHoursTo[RecevingHoursTo.length - 2].value;
      dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsTo`;
    }
    setValue(currentDropdown, dropdownValue.toString());
    if (dropdownValue === 'closed') {
      setValue(adjacentDropdown, dropdownValue.toString());
      setValue(`dates.${dateIndex}.is_user_available`, 0);
    }
    else if (watch(adjacentDropdown) === 'closed') {
      setValue(`dates.${dateIndex}.is_user_available`, 1);
      setValue(adjacentDropdown, onChangingCancelAdjDropDownValue.toString());
    }
    adjDropDownOptionsCopy.forEach(timeOption => {
      const time = { ...timeOption };
      if (dropdownValue !== 'closed' && ((!isReceivingHrsFrom && time.value >= dropdownValue) || (isReceivingHrsFrom && time.value <= dropdownValue))) time.disabled = true;
      receivingHrsOption.push(time);
    })
    setValue(dropDownOptionsToBeDisabled, receivingHrsOption);
    saveReceivingHours();
  }

  const deleteResaleCertificateLine = (index) => {
    const resaleCertList = getValues("resaleCertificateList");
    if (resaleCertList?.length) {
      let list = resaleCertList.filter((x: any, i: any) => {
        if (i !== index) {
          return x
        }
      });
      if (list.length <= 0) {
        list = [...list, defaultResaleCertificateLine];
      }
      setValue("resaleCertificateList", list);
      trigger("resaleCertificateList");
    }
  }

  const openDeletePopup = (cert_id, index) => {
    setPayloadData({ cert_id, index });
    setOpenDeleteConfirmation(true)
  }


  const uploadCertFile = async (file, i) => {
    if (file) {
      setValue(`resaleCertificateList.${i}.uploadCertProgress`, true)
      // setValue(`resaleCertificateList.${i}.state_id`, '');
      // setValue(`resaleCertificateList.${i}.expiration_date`, '');
      // setValue(`resaleCertificateList.${i}.status`, null);
      setValue(`resaleCertificateList.${i}.file_name`, file.name);
      let index = file.name.length - 1;
      for (; index >= 0; index--) {
        if (file.name.charAt(index) === '.') {
          break;
        }
      }
      const ext = file.name.substring(index + 1, file.name.length);

      const objectKey = import.meta.env.VITE_ENVIRONMENT + '/' + userData.data.id + '/' + prefixUrl.resaleCertPrefix + '-' + uuidv4() + '.' + ext;

      const payload = {
        data: {
          "bucket_name": import.meta.env.VITE_S3_UPLOAD_SETTINGS_RESALE_CERT_BUCKET_NAME,
          "object_key": objectKey,
          "expire_time": 300

        }
      }
      let setCertUrl = 'https://' + payload.data.bucket_name + ".s3.amazonaws.com/" + payload.data.object_key;
      setValue(`resaleCertificateList.${i}.resaleCertFile`, '')
      axios.post(import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', payload)
        .then(response => {
          const signedUrl = response.data.data;
          axios.put(signedUrl, file)
            .then(async (response) => {
              if (response.status === 200) {
                setValue(`resaleCertificateList.${i}.uploadCertProgress`, false)
                setValue(`resaleCertificateList.${i}.cerificate_url_s3`, setCertUrl)
                await saveResaleCertificates(getValues("resaleCertificateList"))
                // queryClient.invalidateQueries([reactQueryKeys.getBuyingPreference])
                setValue(`resaleCertificateList.${i}.status`, 'Pending')
                setValue(`resaleCertificateList.${i}.state_code`, referenceData?.ref_states?.find((state: any) => state.id === watch(`resaleCertificateList.${i}.state_id`))?.code)
                showCommonDialog(commomKeys.uploadSuccessful, buyerSettingConst.uploadCertDialogContent, commomKeys.actionStatus.success, resetDialogStore, [{ name: commomKeys.successBtnTitle, action: resetDialogStore }])
              }
            })
            .catch(error => {
              console.error(error);
              setShowLoader(false);
              let uploadProgress = null;
              if (getValues(`resaleCertificateList.${i}.cerificate_url_s3`)) {
                uploadProgress = false;
              }
              setValue(`resaleCertificateList.${i}.uploadCertProgress`, uploadProgress)
              showCommonDialog(null, "Something went wrong. Please try again in sometime", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
            }
            );
        })
        .catch(error => {
          console.error(error);
          // setisDataLoad(false);
          setShowLoader(false);
          let uploadProgress = null;
          if (getValues(`resaleCertificateList.${i}.cerificate_url_s3`)) {
            uploadProgress = false;
          }
          setValue(`resaleCertificateList.${i}.uploadCertProgress`, uploadProgress)
          showCommonDialog(null, "Something went wrong. Please try again in sometime", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])

        }
        );

    }
  }

  const saveResaleCertificates = async (resaleCertificates: any) => {
    if (resaleCertificates?.length > 0) {
      let stateCodeText = "";

      let payload = [];
      resaleCertificates.forEach(certificate => {
        // const state = states.find(state => state.id === certificate.stateId);
        // if (state && (certificate.status === 'Pending' || !certificate.status)) {
        //   stateCodeText += state.code + ", ";
        // }

        if (certificate.cerificate_url_s3) {
          payload.push({
            cerificate_url_s3: certificate.cerificate_url_s3,
            expiration_date: certificate.expiration_date,
            file_name: certificate.file_name,
            id: certificate.id,
            state_id: certificate.state_id,
            status: certificate.status,
          })
        }
      });
      try {
        const response = saveUserSettings({ route: '/user/settings/shipment', data: {
            resale_certificate: payload
          }
        });
      } catch (err) {
        console.error(err)
        setShowLoader(false);
      }
    }
  }

  const resaleCertificateListing = watch("resaleCertificateList")?.map((resaleCertificate, index) => {
    return (
      <div key={index} className={styles.deleteCertLine}>

        <ResaleCertificateLineComponent
          index={index}
          resaleCertificate={resaleCertificate}
          deleteResaleCertificateLine={deleteResaleCertificateLine}
          deleteCertificateTooltip={deleteCertificateTooltip}
          openDeletePopup={openDeletePopup}
          register={register}
          uploadCertFile={uploadCertFile}
          States={States}
          ResaleExpiration={ResaleExpiration}
          control={control}
          errors={errors}
          setValue={setValue}
        />
      </div>
    )
  });

  const deleteCerti = () => {
    const payload = {
      data: {
        cert_id: payloadData.cert_id,
      },
    };

    setOpenDeleteConfirmation(false)
    axios.post(import.meta.env.VITE_API_SERVICE + `/user/deleteResaleCert`, payload)
      .then(response => {
        if (response.data.data.error_message) {
          showCommonDialog(null, response.data.data.error_message, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
        }
        else {
          const certId = getValues(`resaleCertificateList.${payloadData.index}.id`)
          if (payloadData.cert_id === certId) {
            deleteResaleCertificateLine(payloadData.index);
          }
        }

      })
      .catch(error => {
        console.error('Error deleting file:', error);
      });
  };

  // Effect to control dropdown visibility based on focus state
  useEffect(() => {
    if (isDeliveryAddressFocused) {
      // When focused, always show all cities if no filter is applied
      if (deliveryAddressInput?.length === 0) {
        setFilteredCities(deliveryDropDownAddresses);
      }
      setIsAddressDropdownOpen(true);
    }
  }, [isDeliveryAddressFocused, deliveryAddressInput]);

  // Function to handle add new destination
  const handleAddNewDestination = () => {
    setFocusedInput('newCompanyAddress');
    setCustomAddressComponentOpen(true);
  };
  const handleCustomAddressComponentClose = async () => {
    setCustomAddressComponentOpen(false);
    setFocusedInput(null);
    if (watch('newCompanyAddress.line1') && watch('newCompanyAddress.city') && watch('newCompanyAddress.state') && watch('newCompanyAddress.zip') && !errors.newCompanyAddress?.state && !errors.newCompanyAddress?.zip) {
      const stateCode = referenceData?.ref_states?.find((state: any) => state.id === watch("newCompanyAddress.state"))?.code
      const newAddr = `${watch('newCompanyAddress.line1')}, ${watch('newCompanyAddress.city')}, ${stateCode}, ${watch('newCompanyAddress.zip')}`
      const newAddressObj = {
        line1: watch('newCompanyAddress.line1'),
        line2: watch('newCompanyAddress.line2') || "", // Optional field
        city: watch('newCompanyAddress.city'),
        stateCode: watch('newCompanyAddress.stateCode'),
        zip: watch('newCompanyAddress.zip')
      };
      setSearchValue(newAddr)
      setDeliveryAddresses([...deliveryDropDownAddresses, newAddressObj]);
      setValue('deliveryAddress', {
        line1: watch('newCompanyAddress.line1'),
        line2: watch('newCompanyAddress.line2') || "",
        city: watch('newCompanyAddress.city'),
        state: watch('newCompanyAddress.state'),
        zip: watch('newCompanyAddress.zip')
      })
      const zipValue = watch('newCompanyAddress.zip');
      const stateValue = watch('newCompanyAddress.state');
      if (zipValue && stateValue) {
        const isZipCodeValid = await handleStateZipValidation('newCompanyAddress.zip', 'newCompanyAddress.state')
          if(isZipCodeValid) {
          setTimeout(() => {
            handleSaveShipmentSettings()
          }, 100)
        }
      }else{
        handleSaveShipmentSettings()
      }
      setValue('newCompanyAddress', {
        line1: "",
        line2: "",
        city: "",
        state: "",
        zip: ""
      })
    }
  }


  const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
    try {
      if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
        setValidationInProgress(false)
        const payload = {
        data: {
          state_id: getValues(stateCode),
          zip_code: parseInt(getValues(zipCode)),
        },
      };
      const checkStateZipResponse = await axios.post(
        import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
        payload
      );
      if (checkStateZipResponse.data.data === true) {
        clearErrors([stateCode, zipCode]);
        return true
      } else {
        setError(stateCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
        setError(zipCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
        return false
        }
        setValidationInProgress(true)
      }
    } catch (err) {
      console.error(err)
    }
  };

  const handleAddNewLine = () => {
    const resaleCertList = getValues("resaleCertificateList")
    setValue(`resaleCertificateList.${resaleCertList?.length}`, defaultResaleCertificateLine)
  }

  const saveReceivingHours = async () => {
    const dates = getValues("dates")
    try {
      const payload = {
          user_delivery_receiving_availability_details: dates.map((date: any) => {
            const { receivingHrsFrom, receivingHrsTo, ...rest } = date;
            return rest;
          })
      }
      saveUserSettings({ route: '/user/settings/shipment', data: payload });
    } catch (err) {
      console.error(err)
    }
  }

  const handleSaveShipmentSettings = async () => {
      const fieldsToValidate = [];
      const userSettingsPayload: any = {};
      try {
        if (watch('addressNickName') && watch('addressNickName') !== "") {
          fieldsToValidate.push('addressNickName');
          userSettingsPayload.address_nickname = watch('addressNickName');
        }
        userSettingsPayload.delivery_appt_required = watch('deliveryApptRequired');
        if (watch('deliveryContactName') && watch('deliveryContactName') !== "") {
          fieldsToValidate.push('deliveryContactName');
          userSettingsPayload.delivery_contact_name = watch('deliveryContactName');
        }

        if (watch('deliveryPhoneNumber') && watch('deliveryPhoneNumber') !== "") {
          const isValid = await trigger('deliveryPhoneNumber');
          if(isValid) {
            userSettingsPayload.delivery_phone = unformatPhoneNumber(watch('deliveryPhoneNumber'));
          }
        }

        if (watch('deliveryEmailAddress') && watch('deliveryEmailAddress') !== "") {
          const isValid = await trigger('deliveryEmailAddress');
          if(isValid) {
            userSettingsPayload.delivery_email_id = watch('deliveryEmailAddress');
          }
        }

        if(watch('shippingDocsEmail') && watch('shippingDocsEmail') !== "") {
          const isValid = await trigger('shippingDocsEmail');
          if(isValid) {
            userSettingsPayload.shipping_docs_to = watch('shippingDocsEmail');
          }
        }

        if (
          watch('deliveryAddress.line1') && watch('deliveryAddress.line1') !== "" &&
          watch('deliveryAddress.city') && watch('deliveryAddress.city') !== "" &&
          watch('deliveryAddress.state') && watch('deliveryAddress.state') !== "" &&
          watch('deliveryAddress.zip') && watch('deliveryAddress.zip') !== ""
        ) {
          // Check if there are existing errors for buyerAddress
          const hasExistingErrors = errors?.deliveryAddress?.line1 ||
            errors?.deliveryAddress?.line2 ||
            errors?.deliveryAddress?.city ||
            errors?.deliveryAddress?.state ||
            errors?.deliveryAddress?.zip;

          // Only add to validation if no existing errors (to preserve manual errors)
          if (!hasExistingErrors) {
            fieldsToValidate.push('deliveryAddress');
            // Add all fields to the payload
            userSettingsPayload.delivery_address = {
              line1: watch('deliveryAddress.line1'),
              line2: watch('deliveryAddress.line2') || "",
              city: watch('deliveryAddress.city'),
              state_id: watch('deliveryAddress.state'),
              zip: watch('deliveryAddress.zip')
            }
          }
        }
        if (fieldsToValidate.length > 0) {
          const isValid = await trigger(fieldsToValidate);
          if (isValid && Object.keys(userSettingsPayload).length > 0) {
            saveUserSettings({ route: '/user/settings/shipment', data: userSettingsPayload });
            // Reset dirty state for successfully validated and saved fields
            fieldsToValidate.forEach((fieldName) => {
              const currentValue = watch(fieldName);
              resetField(fieldName, {
                  defaultValue: currentValue,
                  keepError: false,
                  keepDirty: false,
                  keepTouched: true
              });
          });
          }
        }
      } catch (err) {
        console.error(err)
      }
  }

  // Fresh autocomplete helper functions
  const formatAddressDisplay = (address: any) => {
    if (!address) return '';
    
    const parts = [address.line1];
    if (address.line2 && address.line2.trim()) {
      parts.push(address.line2);
    }
    parts.push(address.city);
    parts.push(address.zip);
    
    return parts.filter(Boolean).join(', ');
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    
    if (value.trim().length === 0) {
      setFilteredAddresses(deliveryAddresses || []);
      return;
    }

    const filtered = (deliveryAddresses || []).filter((address: any) => {
      const searchText = [
        address.line1,
        address.line2,
        address.city,
        address.zip
      ].filter(Boolean).join(' ').toLowerCase();
      
      return searchText.includes(value.toLowerCase());
    });
    
    setFilteredAddresses(filtered);
  };

  const handleAddressSelect = (address: any) => {
    const formattedAddress = formatAddressDisplay(address);
    setSearchValue(formattedAddress);
    setSelectedAddress(address);
    
    // Set form values
    setValue("deliveryAddress.line1", address.line1);
    setValue("deliveryAddress.line2", address.line2 || "");
    setValue("deliveryAddress.city", address.city);
    setValue("deliveryAddress.state", address.state_id);
    setValue("deliveryAddress.zip", address.zip);
    handleSaveShipmentSettings()
  };

  const handleCheckBoxKeyDown = (e: any) => {
    if(e.key === 'Tab'){
      if(!watch('deliveryApptRequired')){
        setActiveTab('PAYMENTS');
      }
    }
  }

  return (
    <div className={clsx(styles.tabContent,styles.tabContentShipment)} ref={shipmentPopupRef}>
      <div className={styles.scrollerContainer}>
      <div className={styles.formContainer}>

        <div className={clsx(styles.formGroupInput, styles.deliveryAddressContainer)}>
          <span className={styles.col1}>
            <label htmlFor="deliveryAddress">
              SELECT DELIVERY ADDRESS
            </label>
          </span>
          <span className={styles.col1}>
          <div className={clsx(styles.autocompleteContainer,'autocompleteContainer')}>

            <Controller
              name="deliveryAddress"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <Autocomplete
                  freeSolo
                  options={filteredAddresses}
                  value={selectedAddress}
                  inputValue={searchValue}
                  getOptionLabel={(option) => {
                    if (typeof option === 'string') return option;
                    return formatAddressDisplay(option);
                  }}
                  isOptionEqualToValue={(option, value) => {
                    if (!option || !value) return false;
                    return option.id === value.id;
                  }}
                  onInputChange={(event, newInputValue, reason) => {
                    setSearchValue(newInputValue);
                    handleSearchChange(newInputValue);
                    // field.onChange(newInputValue);
                  }}
                  onKeyDown={(e) => {
                    if(e.key === 'Tab'){
                      if(e.shiftKey){
                        setActiveTab('USER');
                      }
                    }
                  }}
                  onChange={(event, newValue) => {
                    if (newValue && typeof newValue === 'object') {
                      handleAddressSelect(newValue);
                    } else if (typeof newValue === 'string') {
                      setSearchValue(newValue);
                      // field.onChange(newValue);
                    }
                  }}
                  classes={{
                    paper: styles.autocompleteDropdown,
                    listbox: styles.autocompleteListbox,
                    option: styles.autocompleteOption,
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Type to search for delivery addresses"
                      error={!!errors?.deliveryAddress}
                      helperText={errors?.deliveryAddress?.message as string}
                      className={styles.deliveryAddressInput}
                      variant="outlined"
                      fullWidth
                    />
                  )}
                  renderOption={(props, option) => (
                    <li {...props} key={`${option.id}-${option.line1}-${option.city}`}>
                      {formatAddressDisplay(option)}
                    </li>
                  )}
                  noOptionsText="No addresses found"
                  className={styles.autocompleteContainer}
                />
              )}
            />
               <button
              className={styles.addNewDestinationBtn}
              onClick={handleAddNewDestination}
            >
              ADD NEW DESTINATION
            </button>
            </div>
         
          </span>
        </div>

        {/* SEND SHIPPING */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label>ADDRESS NICKNAME</label>
          </span>
          <span className={styles.col1}>
            <InputWrapper>
              <CustomTextField
                className={clsx(styles.inputCreateAccount, errors?.addressNickName && styles.error)}
                type='text'
                register={register("addressNickName")}
                placeholder='Example: Houston Yard'
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register("addressNickName").onBlur(e);
                  handleInputBlur('addressNickName')
                }}
                onFocus={() => handleInputFocus('addressNickName')}
                errorInput={errors?.addressNickName}
              />
            </InputWrapper>
          </span>
        </div>

        <div className={clsx(styles.formGroupInput, styles.resaleCertContainer)}>
          <span className={styles.col1}>
            <label htmlFor="deliveryApptRequired">
              RESALE CERTIFICATE
            </label>
            
          </span>
          <span className={styles.col1}>
            <div className={styles.resaleCertContainerInner}>
              <span className={styles.lblGrid}>
                <div className={styles.lblrow}>
                 <span className={styles.lbl}>UPLOAD</span>
                 <span className={styles.lbl}>STATE</span>
                 <span className={styles.lbl}>EXPIRATION</span>
                 <span className={styles.lbl}>STATUS</span>
              </div>
              <div className={styles.resaleCertScroll}>
                {resaleCertificateListing}
              </div>
              </span>
              <button className={styles.addAnotherCertBtn} onClick={handleAddNewLine}>ADD ANOTHER RESALE CERTIFICATE</button>
            </div> 
          </span>
        </div>

        <div className={clsx(styles.formGroupInput, styles.receivingHoursInput)}>
          <span className={styles.col1}>
            <label htmlFor="deliveryApptRequired">
              RECEIVING HOURS
            </label>
          </span>
          <span className={styles.col1}>
            {watch('dates')?.map((x: any, i: any) => (<span key={x.day} className={styles.inputSectionRecevingHours}>
              <span className={`${watch(`dates.${i}.from`) !== 'closed' ? styles.daylbl1 : styles.daylbl1}`}>{x.display_name}</span>
              <span className={clsx(styles.daylbl2, 'w100 dflex')}>
                <CustomMenu
                  control={control}
                  defaultValue={x.from}
                  name={`dates.${i}.from`}
                  // className={'selectReceivingHours selectUploadCertDropdown'}
                  className={clsx((!dirtyFields.dates?.[i]?.from  && 'disabledDropdown'),(x.from === 'closed' && 'txtClosed') ,'selectReceivingHours selectUploadCertDropdown')}
                  MenuProps={MenuPropsTop}
                  items={x.receivingHrsFrom}
                  IconComponent={DropdownIcon}
                  onChange={(events: any) => {
                    changeReceivingHrs(i, true, events.target.value);
                  }}
                />
              </span>
              <span className={clsx(styles.daylbl3, 'w100 dflex')}>
                <CustomMenu
                  defaultValue={x.to}
                  control={control}
                  name={`dates.${i}.to`}
                 className={clsx((!dirtyFields.dates?.[i]?.to  && 'disabledDropdown'), (x.to === 'closed' && 'txtClosed') ,'selectReceivingHours selectUploadCertDropdown')}
                  MenuProps={MenuPropsBottom}
                  IconComponent={DropdownIcon}
                  items={x.receivingHrsTo}
                  onChange={(events: any) => {
                    changeReceivingHrs(i, false, events.target.value);
                  }}
                />
              </span>
            </span>))}
          </span>
        </div>

        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label htmlFor="deliveryApptRequired">
              DELIVERY APPT REQUIRED?
            </label>
          </span>
          <span className={styles.col1}>
            <CustomToggleCheckbox
              name="deliveryApptRequired"
              control={control}
              onKeyDown={handleCheckBoxKeyDown}
              onChange={
                (e: any) => {
                  setValue('deliveryApptRequired', e);
                  handleSaveShipmentSettings();
                }
              }
            />
          </span>
        </div>

        {/* Delivery fields wrapper - apply hide class to parent */}
        <div className={clsx(!deliveryApptRequired && styles.hide)}>
          {/* DELIVERY CONTACT NAME */}
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.deliveryContactName && styles.focusLbl)} htmlFor="deliveryContactName">
                DELIVERY CONTACT NAME
              </label>
            </span>
            <span className={styles.col1}>
              <InputWrapper>
                <CustomTextField
                tabIndex={watch('deliveryApptRequired') ? 0 : -1}
                  className={clsx(styles.inputCreateAccount, errors?.deliveryContactName && styles.error)}
                  type='text'
                  register={register("deliveryContactName")}
                  placeholder=''
                  onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                    register("deliveryContactName").onBlur(e);
                    handleInputBlur('deliveryContactName')
                  }}
                  onFocus={() => handleInputFocus('deliveryContactName')}
                  errorInput={errors?.deliveryContactName}
                />
              </InputWrapper>
            </span>
          </div>

          {/* DELIVERY PHONE NUMBER */}
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.deliveryPhoneNumber && styles.focusLbl)} htmlFor="deliveryPhoneNumber">
                DELIVERY PHONE NUMBER
              </label>
            </span>
            <span className={styles.col1}>
              <InputWrapper>
                <CustomTextField
                tabIndex={watch('deliveryApptRequired') ? 0 : -1}
                  className={clsx(styles.inputCreateAccount, errors?.deliveryPhoneNumber && styles.error)}
                  type='tel'
                  register={register("deliveryPhoneNumber")}
                  placeholder=''
                  onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                    register("deliveryPhoneNumber").onBlur(e);
                    handleInputBlur('deliveryPhoneNumber')
                  }}
                  onFocus={() => handleInputFocus('deliveryPhoneNumber')}
                  errorInput={errors?.deliveryPhoneNumber}
                  mode="phoneNumberHyphen"
                />
              </InputWrapper>
            </span>
          </div>

          {/* DELIVERY EMAIL ADDRESS */}
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.deliveryEmailAddress && styles.focusLbl)} htmlFor="deliveryEmailAddress">
                DELIVERY EMAIL ADDRESS
              </label>
            </span>
            <span className={styles.col1}>
              <InputWrapper>
                <CustomTextField
                tabIndex={watch('deliveryApptRequired') ? 0 : -1}
                  className={clsx(styles.inputCreateAccount, errors?.deliveryEmailAddress && styles.error)}
                  type='email'
                  register={register("deliveryEmailAddress")}
                  placeholder=''
                  onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                    register("deliveryEmailAddress").onBlur(e);
                    handleInputBlur('deliveryEmailAddress')
                  }}
                  onFocus={() => handleInputFocus('deliveryEmailAddress')}
                  errorInput={errors?.deliveryEmailAddress}
                />
              </InputWrapper>
            </span>
          </div>

          {/* SEND SHIPPING */}
          <div className={clsx(styles.formGroupInput,styles.bdrBtm0)}>
            <span className={styles.col1}>
              <label>SEND SHIPPING DOCS TO</label>
            </span>
            <span className={styles.col1}>
              <InputWrapper>
                <CustomTextField
                tabIndex={watch('deliveryApptRequired') ? 0 : -1}
                  className={clsx(styles.inputCreateAccount, errors?.shippingDocsEmail && styles.error)}
                  type='email'
                  register={register("shippingDocsEmail")}
                  placeholder=''
                  onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                    register("shippingDocsEmail").onBlur(e);
                    handleInputBlur('shippingDocsEmail')
                  }}
                  onFocus={() => handleInputFocus('shippingDocsEmail')}
                  errorInput={errors?.shippingDocsEmail}
                />
              </InputWrapper>
            </span>
          </div>
        </div>

      </div>
      </div>
      <Dialog
        open={openDeleteConfirmation}
        transitionDuration={200}
        hideBackdrop
        classes={{
          root: styles.ErrorDialog,
          paper: styles.dialogContent
        }}
        container={shipmentPopupRef.current}
        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
      }}
      PaperProps={{
          style: {
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              margin: 0
          }
      }}
      >
        <div>
          <p>Are you sure you want to delete the file?</p>
          <div className={styles.deleteBtnSection}>
            <button className={styles.submitBtn} onClick={deleteCerti}>Yes</button>
            <button className={styles.submitBtn} onClick={() => setOpenDeleteConfirmation(false)}>No</button>
          </div>
        </div>
      </Dialog>
      <Dialog
        open={customAddressComponentOpen && focusedInput}
        onClose={(event) =>  handleCustomAddressComponentClose()}
        transitionDuration={100}
        disableScrollLock={true}
        container={shipmentPopupRef.current}
        
                style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(7px)',
                    WebkitBackdropFilter: 'blur(7px)',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    border: '1px solid transparent',
                    borderRadius: '0px 0px 20px 20px',
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0
                    }
                }}
                hideBackdrop
                classes={{
                    root: styles.customeAddressPopup,
                    paper: styles.dialogContent
                }}
      >
        <button className={styles.closeIcon} onClick={(event) => handleCustomAddressComponentClose()}><CloseIcon /></button>
        <CustomAddressComponent
          focusedInput={focusedInput}
          States={States}
          register={register}
          handleInputBlur={handleInputBlur}
          handleInputFocus={handleInputFocus}
          errors={errors}
          control={control}
          setValue={setValue}
        />
      </Dialog>
    </div>
  );
};

export default ShipmentsTab;


