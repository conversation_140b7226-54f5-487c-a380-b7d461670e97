// @ts-nocheck
import React, { useState, useEffect, useMemo } from 'react';
import styles from './SharedPricingHistoryWindow.module.scss';
import clsx from 'clsx';
import { ReactComponent as SearchIconSvg } from '../../assets/New-images/Search.svg';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import { ReactComponent as BackIcon } from '../../assets/New-images/Create-Account/arrow-left.svg';
import useGetSharedProductPrices from '../../hooks/useGetSharedProductPrices';
import Loader from 'src/renderer2/Loader/Loader';
import { Tooltip, Fade } from '@mui/material';
import { formatNumericValue } from 'src/renderer2/common';
import { formatDollarPerUnit, priceUnits } from '@bryzos/giss-ui-library';

interface Product {
  product_id: number;
  product_description: string;
  price: number;
  price_unit: string;
  domestic_material_only?: boolean;
}

interface HistoryItem {
  shared_pricing_date_time: string;
  item_count: number;
  zipcode: string;
  order_size: string;
  id: string;
  to_email: string;
  email_content?: string;
  products?: Product[];
}


// const ProductDescription: React.FC<{ description: string }> = ({ description }) => {
//   const lines = description.split('\r\n');
//   const [firstLine, ...restLines] = lines;
  
//   return (
//     <div className={styles.priceItemTitle}>
//       {firstLine}
//       {restLines.map((line, index) => (
//         <div key={index} className={styles.priceItemDetails}>
//           {line}
//         </div>
//       ))}
//     </div>
//   );
// };

const SharedPricingHistoryWindow: React.FC = () => {
  const { setIsSharedPricingHistory } = useRightWindowStore();
  const [searchValue, setSearchValue] = useState<string>('');
  const [selectedItem, setSelectedItem] = useState<HistoryItem | null>(null);
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  
  // Use the updated useQuery hook
  const { 
    data: sharedPricesData, 
    isLoading, 
    error,
    refetch: refetchSharedPrices
  } = useGetSharedProductPrices();

  useEffect(() => {
    return () => {
      setIsSharedPricingHistory(false);
    }
  }, [])

  // Update historyItems whenever data changes
  useEffect(() => {
    if (sharedPricesData?.data?.data && Array.isArray(sharedPricesData.data.data)) {
      setHistoryItems(sharedPricesData.data.data);
    } else {
      setHistoryItems([]);
    }
  }, [sharedPricesData, isLoading]);

  const filteredItems = useMemo(() => {
    if (!searchValue) return historyItems;
    
    const lowercasedSearch = searchValue.toLowerCase();
    return historyItems.filter(item => {
      const toEmail = item.to_email?.toLowerCase() || '';
      const dateTime = item.shared_pricing_date_time?.toLowerCase() || '';
      const zipcode = item.zipcode?.toLowerCase() || '';
      const orderSize = formatNumericValue(item?.order_size, 4);
      const itemCount = item.item_count ? item.item_count.toString() : '';


      return toEmail.includes(lowercasedSearch) ||
             dateTime.includes(lowercasedSearch) ||
             zipcode.includes(lowercasedSearch) ||
             orderSize.includes(lowercasedSearch) ||
             itemCount.includes(lowercasedSearch);
    });
  }, [searchValue, historyItems]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleItemClick = (item: HistoryItem) => {
    setSelectedItem(item);
  };

  const handleBackClick = () => {
    setSelectedItem(null);
  };

  const renderEmailContent = (content: string) => {
    return (
      <div 
        className={styles.emailContent}
        dangerouslySetInnerHTML={{ __html: content.replace(/\n/g, '<br>') }}
      />
    );
  };

    const renderHistoryItem = (item: HistoryItem) => {
      const orderSize = formatNumericValue(item?.order_size, 4);
      return (
    <div
      key={item.id}
      className={clsx(styles.historyItem)}
      onClick={() => handleItemClick(item)}
    >
      <div className={styles.historyItemTitle}>
        To: <span>{item.to_email}</span>
      </div>
      <div className={styles.historyItemDetails}>
        <div className={styles.detailsRow}>
          <span>{item.shared_pricing_date_time}</span>
        </div>
        <div className={clsx(styles.detailsRow,styles.detailsRowGrid)}>
          <span>{item.item_count} Items</span>
          <span>Zip {item.zipcode}</span>
        </div>
        <div className={clsx(styles.detailsRow,styles.detailsRowGrid)}>
          <span>Based Upon</span>
          <span>{orderSize} LBS</span>
        </div>
      </div>
    </div>
  )};  

  const renderHistoryList = () => (
    <div className={styles.historyList}>
      <div className={styles.historyItemList}>
      {isLoading ? (
        <div className={styles.loading}><Loader/></div>
      ) : error ? (
        <div className={styles.error}>{'Failed to load shared pricing history'}</div>
      ) : filteredItems.length > 0 ? (
        filteredItems.map(renderHistoryItem)
      ) : (
        <div className={styles.noResults}>No matching results</div>
      )}
      </div>
    </div>
  );

  const renderPriceItem = (item: Product) => {
    const priceValue = formatDollarPerUnit(item.price_share_type.toLowerCase(), item[`price_${item.price_share_type.toLowerCase()}`],0)
      
    return(
    <div  className={styles.priceItemContent}>
      <div key={item.product_id} className={styles.priceRow}>
        <span className={clsx(styles.productDescription1,item.product_description.split('\n')[0]?.includes('Miscellaneous') && styles.miscDesc)}>{item.product_description.split('\n')[0]}</span>
        <div className={styles.priceItemBottom}>
        <div className={styles.priceItemTitle}>
          {item.product_description.split('\n').slice(1).map((line, index) => (
            <div key={index} className={styles.priceItemDetails}>
              {line}
            </div>
          ))}
        
        </div>
          <div className={styles.priceValue}>
          <div className={styles.price}><span className={styles.textStyle1}>$</span><span>{priceValue}</span></div>
          <div className={styles.priceUnit}>PER {item.price_share_type.toUpperCase()}</div>
        </div>
        </div>
      </div>
      {item.domestic_material_only && (
        <div className={styles.domesticBadge}>Domestic Material Only</div>
      )}
    </div>
  )};

  const renderDetailView = () => {
    if (!selectedItem) return null;
    const orderSize = formatNumericValue(selectedItem?.order_size, 4);

    return (
      <>

         <div className={styles.backButton} onClick={handleBackClick}>
                  <BackIcon/> Back to Price Share History
          </div>

        <div className={styles.historyItemExpanded}>
          <div className={styles.expandedHistoryItemTitle}>
            To: <Tooltip
              title={selectedItem.to_email}
              placement="top-start"
              TransitionComponent={Fade}
              TransitionProps={{ timeout: 200 }}
              classes={{
                tooltip: styles.emailTooltip
              }}
            >
              <span>{selectedItem.to_email}</span>
            </Tooltip>
          </div>
          <div className={styles.historyItemDetails}>
            <div className={styles.detailsRow}>
              <span>{selectedItem.shared_pricing_date_time}</span>
              <span>Zip {selectedItem.zipcode}</span>
            </div>
            <div className={styles.detailsRow}>
              <span>{selectedItem.item_count} Items</span>
              <span>Based Upon {orderSize} LBS</span>
            </div>
          </div>
        </div>


        <div className={styles.priceList}>
          <div className={styles.priceItemList}>
            <div className={styles.priceItem}>
             {selectedItem.email_content && renderEmailContent(selectedItem.email_content)}
             {selectedItem.products?.map(renderPriceItem)}
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className={styles.priceSearchHistory}>
      <div className={styles.headerMain}>
      <div className={styles.header}>SHARED PRICING HISTORY</div>
      
      <div className={styles.searchBox}>
        <div className={styles.searchIcon}><SearchIconSvg/></div>
        <input
          type="text"
          className={styles.searchInput}
          placeholder="Search History"
          value={searchValue}
          onChange={handleSearchChange}
          disabled={!!selectedItem}
        />      </div>
      </div>
  
      {selectedItem ? renderDetailView() : renderHistoryList()}
    </div>
  );
};

export default SharedPricingHistoryWindow;
