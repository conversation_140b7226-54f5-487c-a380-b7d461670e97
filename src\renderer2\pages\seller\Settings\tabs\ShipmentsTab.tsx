import React, { useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import { Controller } from 'react-hook-form';
import CustomToggleCheckbox from 'src/renderer2/component/CustomToggleCheckbox';
import clsx from 'clsx';
import { Dialog } from '@mui/material';
import axios from 'axios';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import CustomAddressComponent from '../components/CustomAddressComponent';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import { unformatPhoneNumber } from 'src/renderer2/helper';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import StateSelector from '../components/StateSelector/StateSelector';

interface InputFocusState {
  deliveryContactName: boolean;
  deliveryPhoneNumber: boolean;
  deliveryEmailAddress: boolean;
  shippingDocsEmail: boolean;
  addressNickName: boolean;
  deliveryFullAddress: boolean;
  deliveryAddressCity: boolean;
  mainLocationAddress: boolean;
}

const ShipmentsTab: React.FC<{ mainWrapperRef: React.RefObject<HTMLDivElement>, register: any, handleSubmit: any, control: any, watch: any, setValue: any, getValues: any, setError: any, clearErrors: any, errors: any, buyerSettings: any, trigger: any, isDirty: any, resetField: any, dirtyFields: any, setActiveTab: any }> = ({ mainWrapperRef, register, handleSubmit, control, watch, setValue, getValues, setError, clearErrors, errors, trigger, buyerSettings, trigger: any, isDirty, resetField, dirtyFields, setActiveTab }) => {
  const { userData, showLoader, setShowLoader, referenceData }: any = useGlobalStore();
  const [States, setStates] = useState<Array<{ id: number | string, code: string }>>([]);
  const [customAddressComponentOpen, setCustomAddressComponentOpen] = useState(false);
  const [focusedInput, setFocusedInput] = useState<string | null>(null);
  const [validationInProgress, setValidationInProgress] = useState(true);
  const shipmentPopupRef = useRef(null);
  const { mutate: saveUserSettings } = useSaveUserSettings();

  useEffect(() => {
    if (userData.data.id && referenceData) {
      setStates(referenceData.ref_states);
    }
  }, [referenceData, userData]);

  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    deliveryContactName: false,
    deliveryPhoneNumber: false,
    deliveryEmailAddress: false,
    shippingDocsEmail: false,
    deliveryFullAddress: false,
    deliveryAddressCity: false,
    addressNickName: false,
    mainLocationAddress: false,
  });

  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
    if (dirtyFields[inputName]) {
      if (focusedInput !== 'mainLocationAddress') {
        setTimeout(() => {
          handleSaveShipmentSettings()
        }, 100)
      }
    }
  }

  const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
    try {
      if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
        setValidationInProgress(false)
        const payload = {
          data: {
            state_id: getValues(stateCode),
            zip_code: parseInt(getValues(zipCode)),
          },
        };
        const checkStateZipResponse = await axios.post(
          import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
          payload
        );
        if (checkStateZipResponse.data.data === true) {
          clearErrors([stateCode, zipCode]);
          return true
        } else {
          setError(stateCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
          setError(zipCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
          return false
        }
        setValidationInProgress(true)
      }
    } catch (err) {
      console.error(err)
    }
  };

  const handleSaveShipmentSettings = async () => {
    const fieldsToValidate = [];
    const userSettingsPayload: any = {};
    try {
      if (watch('addressNickName') && watch('addressNickName') !== "") {
        fieldsToValidate.push('addressNickName');
        userSettingsPayload.address_nickname = watch('addressNickName');
      }
      userSettingsPayload.delivery_appt_required = watch('deliveryApptRequired');
      if (watch('deliveryContactName') && watch('deliveryContactName') !== "") {
        fieldsToValidate.push('deliveryContactName');
        userSettingsPayload.delivery_contact_name = watch('deliveryContactName');
      }

      if (watch('deliveryPhoneNumber') && watch('deliveryPhoneNumber') !== "") {
        const isValid = await trigger('deliveryPhoneNumber');
        if (isValid) {
          userSettingsPayload.delivery_phone = unformatPhoneNumber(watch('deliveryPhoneNumber'));
        }
      }

      if (watch('deliveryEmailAddress') && watch('deliveryEmailAddress') !== "") {
        const isValid = await trigger('deliveryEmailAddress');
        if (isValid) {
          userSettingsPayload.delivery_email_id = watch('deliveryEmailAddress');
        }
      }

      if (watch('shippingDocsEmail') && watch('shippingDocsEmail') !== "") {
        const isValid = await trigger('shippingDocsEmail');
        if (isValid) {
          userSettingsPayload.shipping_docs_to = watch('shippingDocsEmail');
        }
      }

      if (
        watch('deliveryAddress.line1') && watch('deliveryAddress.line1') !== "" &&
        watch('deliveryAddress.city') && watch('deliveryAddress.city') !== "" &&
        watch('deliveryAddress.state') && watch('deliveryAddress.state') !== "" &&
        watch('deliveryAddress.zip') && watch('deliveryAddress.zip') !== ""
      ) {
        // Check if there are existing errors for buyerAddress
        const hasExistingErrors = errors?.deliveryAddress?.line1 ||
          errors?.deliveryAddress?.line2 ||
          errors?.deliveryAddress?.city ||
          errors?.deliveryAddress?.state ||
          errors?.deliveryAddress?.zip;

        // Only add to validation if no existing errors (to preserve manual errors)
        if (!hasExistingErrors) {
          fieldsToValidate.push('deliveryAddress');
          // Add all fields to the payload
          userSettingsPayload.delivery_address = {
            line1: watch('deliveryAddress.line1'),
            line2: watch('deliveryAddress.line2') || null,
            city: watch('deliveryAddress.city'),
            state_id: watch('deliveryAddress.state'),
            zip: watch('deliveryAddress.zip')
          }
        }
      }
      if (fieldsToValidate.length > 0) {
        const isValid = await trigger(fieldsToValidate);
        if (isValid && Object.keys(userSettingsPayload).length > 0) {
          console.log(userSettingsPayload)
          // saveUserSettings({ route: '/user/settings/shipment', data: userSettingsPayload });
          // Reset dirty state for successfully validated and saved fields
          fieldsToValidate.forEach((fieldName) => {
            const currentValue = watch(fieldName);
            resetField(fieldName, {
              defaultValue: currentValue,
              keepError: false,
              keepDirty: false,
              keepTouched: true
            });
          });
        }
      }
    } catch (err) {
      console.error(err)
    }
  }

  const handleCheckBoxKeyDown = (e: any) => {
    if (e.key === 'Tab') {
      if (!watch('orderClaimPreferences')) {
        setActiveTab('PAYMENTS');
      }
    }
  }

  const handleMainLocationAddressClick = () => {
    setFocusedInput('mainLocationAddress');
    setCustomAddressComponentOpen(true);

  }

  const handleMainLocationAddressKeyDown = (e: React.KeyboardEvent<HTMLSpanElement>) => {
    if (e.key === 'Enter') {
      handleMainLocationAddressClick();
    }
  }

  const handleCustomAddressComponentClose = async () => {
    setCustomAddressComponentOpen(false);
    if (focusedInput && dirtyFields[focusedInput]) {
      if (focusedInput === 'mainLocationAddress') {
        // Only validate if both zip and state have values
        const zipValue = watch(`${focusedInput}.zip`);
        const stateValue = watch(`${focusedInput}.state`);
        if (zipValue && stateValue) {
          const isZipCodeVaild = await handleStateZipValidation(`${focusedInput}.zip`, `${focusedInput}.state`)
          if (isZipCodeVaild) {
            setTimeout(() => {
              handleSaveShipmentSettings();
            }, 100)
          }
        } else {
          setTimeout(() => {
            handleSaveShipmentSettings();
          }, 100)
        }
      }
    }
    setFocusedInput(null);
  }

  return (
    <div className={clsx(styles.tabContent, styles.tabContentShipment)} ref={shipmentPopupRef}>
      <div className={styles.scrollerContainer}>
        <div className={styles.formContainer}>

          <div className={clsx(styles.formGroupInput, styles.deliveryAddressContainer)}>
            <span className={styles.col1}>
              <label htmlFor="deliveryAddress">
                MAIN STOCKING LOCATION
              </label>
            </span>
            <span className={styles.col1}>
              {
                <span
                  onFocus={() => {
                    setIsInputFocused((prevState) => ({
                      ...prevState,
                      buyerAddress: true,
                    }));
                  }}
                  onBlur={() => {
                    setIsInputFocused((prevState) => ({
                      ...prevState,
                      buyerAddress: false,
                    }));
                  }}
                  tabIndex={0} onClick={handleMainLocationAddressClick} onKeyDown={handleMainLocationAddressKeyDown} className={clsx(styles.inputCreateAccount, (errors.mainLocationAddress) && styles.error)}>
                  <span className={styles.locationAdressPreviewContainer}>
                    {[
                      watch("mainLocationAddress.line1"),
                      watch("mainLocationAddress.line2"),
                      watch("mainLocationAddress.city"),
                      watch("mainLocationAddress.stateCode"),
                      watch("mainLocationAddress.zip")
                    ].filter(Boolean).join(", ")}
                  </span>
                </span>
              }

            </span>
          </div>
          <div className={clsx(styles.formGroupInput, styles.whereCanYouFulfillOrders)}>
            <span className={styles.col1}>
              <label>
                WHERE CAN YOU FULFILL ORDERS?
              </label>
            </span>
            <div className={styles.stateDropdownContainer}>
              <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
                <button type="button" className={styles.submitBtn} onClick={() => setValue('fulfillableStates', States.map(s => s.id), { shouldDirty: true })}>Select All</button>
                <button type="button" className={styles.submitBtn} onClick={() => setValue('fulfillableStates', [], { shouldDirty: true })}>Deselect All</button>
              </div>
              <Controller
                name="fulfillableStates"
                control={control}
                render={({ field }) => (
                  <StateSelector
                    allStates={States}
                    selectedStates={field.value || []}
                    onChange={(newSelection) => {
                      field.onChange(newSelection);
                      setValue('fulfillableStates', newSelection, { shouldDirty: true });
                    }}
                  />
                )}
              />
            </div>
          </div>

          <div className={clsx(styles.formGroupInput, styles.whereCanYouFulfillOrders)}>
            <span className={styles.col1}>
              <label>
                ORDER CLAIM PREFERENCES
              </label>
            </span>
            <span className={styles.col1}>
              <CustomToggleCheckbox
                name="orderClaimPreferences"
                control={control}
                onKeyDown={handleCheckBoxKeyDown}
                onChange={
                  (e: any) => {
                    setValue('orderClaimPreferences', e);
                    handleSaveShipmentSettings();
                  }
                }
              />
            </span>
            This setting will apply your selected locations to your Available Orders screen. You will only see Purchase Orders destined for delivery to your selected locations above. Select 'Yes' to apply this setting.
          </div>
        </div>
      </div>
      <Dialog
        open={customAddressComponentOpen && !!focusedInput}
        onClose={(event) => handleCustomAddressComponentClose()}
        transitionDuration={100}
        disableScrollLock={true}
        container={shipmentPopupRef.current}

        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          border: '1px solid transparent',
          borderRadius: '0px 0px 20px 20px',
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0
          }
        }}
        hideBackdrop
        classes={{
          root: styles.customeAddressPopup,
          paper: styles.dialogContent
        }}
      >
        <button className={styles.closeIcon} onClick={(event) => handleCustomAddressComponentClose()}><CloseIcon /></button>
        <CustomAddressComponent
          focusedInput={focusedInput}
          States={States}
          register={register}
          handleInputBlur={handleInputBlur}
          handleInputFocus={handleInputFocus}
          errors={errors}
          control={control}
          setValue={setValue}
          setCustomAddressComponentOpen={setCustomAddressComponentOpen}
          setFocusedInput={setFocusedInput}
        />
      </Dialog>
    </div>
  );
};

export default ShipmentsTab;


