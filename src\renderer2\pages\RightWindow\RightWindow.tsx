import clsx from 'clsx';
import { useRightWindowStore } from './RightWindowStore';
import styles from './rightWindow.module.scss';
import { getChannelWindow } from '@bryzos/giss-ui-library';
import { useRef, useEffect } from 'react';
import { ReactComponent as AcrylicCorner} from '../../assets/New-images/corner3.svg';
import VideoPlayerRightWindow from './VideoPlayerRightWindow/VideoPlayerRightWindow';

const RightWindow = ({rightWindowRef, routerContentRef, updateBackdropOverlay}: {rightWindowRef: React.RefObject<HTMLDivElement>, routerContentRef: React.RefObject<HTMLDivElement>, updateBackdropOverlay: boolean}) => {
    const { loadComponent, toolTipVideoComponent, showVideo, setShowVideo } = useRightWindowStore();
    const channelWindow:any = getChannelWindow();
    const divRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (rightWindowRef?.current && routerContentRef?.current) {
            const mainContentHeight = routerContentRef.current.clientHeight;
            const { isPriceSearchHistory, isSharedPricingHistory } = useRightWindowStore.getState();
            rightWindowRef.current.style.maxHeight = `${mainContentHeight}px`;
            
            if (isPriceSearchHistory || isSharedPricingHistory) {
                rightWindowRef.current.style.height = `${mainContentHeight}px`;
            } else {
                // For other pages, apply default height behavior
                rightWindowRef.current.style.height = 'auto';
            }
        }
    }, [routerContentRef?.current?.clientHeight, loadComponent]);

    const ignoreMouseEvents = () => {
        setTimeout(() => {
            handleMouseLeave();
        }, 1000);
    }

    const handleMouseEnter = () => {
        if(channelWindow?.ignoreMouseEvents){
            window.electron.send({ channel: channelWindow.ignoreMouseEvents, data:true})
        }
    }

    const handleMouseLeave = () => {
        if(channelWindow?.ignoreMouseEvents){
            window.electron.send({ channel: channelWindow.ignoreMouseEvents, data:false})
        }
    }

    if(!!!loadComponent && !toolTipVideoComponent){
        return <></>
    }
    return (
        <div className={styles.rightWindowContainer}>
            {(updateBackdropOverlay) && 
                <div className='backdropOverlay' />
            }
            <div className={styles.rightWindow} ref={rightWindowRef}>
                <div className={clsx('wrapperOverlay2', styles.wrapperOverlay)}></div>
                {toolTipVideoComponent?toolTipVideoComponent:loadComponent}
                {/* {toolTipVideoComponent} */}
            </div>
            {(routerContentRef?.current?.clientHeight !== rightWindowRef?.current?.clientHeight) && (
                <div className={styles.nonFrameContainer}
                    onClick={ignoreMouseEvents}
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                    ref={divRef}>
                    <div className={styles.roundCorner}><AcrylicCorner /></div>
                </div>)
            }
        </div>
    )
}

export default RightWindow;
