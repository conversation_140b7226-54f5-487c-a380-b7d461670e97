import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../common";

const useGetSaveSearchProducts = () => {
  return useQuery({
    queryKey: [reactQueryKeys.getSaveSearchProducts],
    queryFn: async () => {
      try {
        const url = `${import.meta.env.VITE_API_SERVICE}/user/search-products`;
        const response = await axios.get(url);
        return response;
      } catch (error) {
        throw error;
      }
    },
    staleTime: 0,
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: true
  });
};

export default useGetSaveSearchProducts; 