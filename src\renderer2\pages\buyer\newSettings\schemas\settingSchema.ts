import * as yup from 'yup';

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const settingSchema = yup.object().shape({
  // Placeholder for User tab fields
  userType: yup.string(),
  firstName: yup.string(),
  email: yup.string().email('Invalid email format').test('is-email', 'Invalid email format', function(value) {
    if(!value) return true;
    return isEmail(value);
  }),
  phoneNumber: yup.string().min(12, 'Phone number is not valid'),
  searchZipcode: yup.string().min(5, 'Zipcode is not valid'),

  parentCompanyName: yup.string(),
  companyDBAName: yup.string(),
  companyType: yup.string(),

  companyAddress: yup.object().shape({
    line1: yup.string(),
    line2: yup.string(),
    city: yup.string(),
    state: yup.string(),
    stateCode: yup.string(),
    zip: yup.string()
  }).test('all-or-none', 'If any address field is filled, all fields are required', function(value) {
    const { line1, city, state, zip } = value || {};
    
    // Check if any field is filled
    const hasAnyValue = line1 || city || state || zip;
    
    // If no fields are filled, that's valid (optional)
    if (!hasAnyValue) {
      return true;
    }
    
    // If any field is filled, all must be filled
    const allFieldsFilled = line1 && city && state && zip;
    
    if (!allFieldsFilled) {
      // Return specific error for missing fields
      if (!line1) return this.createError({ path: `${this.path}.line1`, message: 'Line 1 is required when any address field is filled' });
      if (!city) return this.createError({ path: `${this.path}.city`, message: 'City is required when any address field is filled' });
      if (!state) return this.createError({ path: `${this.path}.state`, message: 'State is required when any address field is filled' });
      if (!zip) return this.createError({ path: `${this.path}.zip`, message: 'Zip code is required when any address field is filled' });
    }
    
    return true;
  }),

  billingContactName: yup.string(),
  billingContactEmail: yup.string().email('Invalid email format').test('is-email', 'Invalid email format', function(value) {
    if(!value) return true;
    return isEmail(value);
  }),

  buyerAddress: yup.object().shape({
    line1: yup.string(),
    line2: yup.string(),
    city: yup.string(),
    state: yup.string(),
    stateCode: yup.string(),
    zip: yup.string()
  }).test('all-or-none', 'If any address field is filled, all fields are required', function(value) {
    const { line1, line2, city, state, zip } = value || {};
    
    // Check if any field is filled
    const hasAnyValue = line1 || city || state || zip;
    
    // If no fields are filled, that's valid (optional)
    if (!hasAnyValue) {
      return true;
    }
    
    // If any field is filled, all must be filled
    const allFieldsFilled = line1 && city && state && zip;
    
    if (!allFieldsFilled) {
      // Return specific error for missing fields
      if (!line1) return this.createError({ path: `${this.path}.line1`, message: 'Line 1 is required when any address field is filled' });
      if (!city) return this.createError({ path: `${this.path}.city`, message: 'City is required when any address field is filled' });
      if (!state) return this.createError({ path: `${this.path}.state`, message: 'State is required when any address field is filled' });
      if (!zip) return this.createError({ path: `${this.path}.zip`, message: 'Zip code is required when any address field is filled' });
    }
    
    return true;
  }),

  newCompanyAddress: yup.object().shape({
    line1: yup.string(),
    line2: yup.string(),
    city: yup.string(),
    state: yup.string(),
    stateCode: yup.string(),
    zip: yup.string()
  }).test('all-or-none', 'If any address field is filled, all fields are required', function(value) {
    const { line1, city, state, zip } = value || {};
    
    // Check if any field is filled
    const hasAnyValue = line1 || city || state || zip;
    
    // If no fields are filled, that's valid (optional)
    if (!hasAnyValue) {
      return true;
    }
    
    // If any field is filled, all must be filled
    const allFieldsFilled = line1 && city && state  && zip;
    
    if (!allFieldsFilled) {
      // Return specific error for missing fields
      if (!line1) return this.createError({ path: `${this.path}.line1`, message: 'Line 1 is required when any address field is filled' });
      if (!city) return this.createError({ path: `${this.path}.city`, message: 'City is required when any address field is filled' });
      if (!state) return this.createError({ path: `${this.path}.state`, message: 'State is required when any address field is filled' });
      if (!zip) return this.createError({ path: `${this.path}.zip`, message: 'Zip code is required when any address field is filled' });
    }
    
    return true;
  }),

  sendInvoicesTo: yup.string().trim().test('valid-emails', 'Send Invoices to is not valid', value => {
    if (!value) return true;
    const emails = value.split(',');
    const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
    return isValid;
  }),
  sendRemittancesTo: yup.string().trim().test('valid-emails', 'Enter valid email', value => {
    if (!value) return true;
    const emails = value.split(',');
    const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
    return isValid;
  }),


  deliveryApptRequired: yup.boolean().default(false),
  deliveryContactName: yup.string(),
  deliveryPhoneNumber: yup.string().min(12, 'Phone number is not valid'),
  deliveryAddress:yup.object().shape({
    line1: yup.string(),
    line2: yup.string(),
    city: yup.string(),
    state: yup.string(),
    stateCode: yup.string(),
    zip: yup.string()
  }).test('all-or-none', 'If any address field is filled, all fields are required', function(value) {
    const { line1, city, state, zip } = value || {};
    
    // Check if any field is filled
    const hasAnyValue = line1 || city || state  || zip;
    
    // If no fields are filled, that's valid (optional)
    if (!hasAnyValue) {
      return true;
    }
    
    // If any field is filled, all must be filled
    const allFieldsFilled = line1 && city && state  && zip;
    
    if (!allFieldsFilled) {
      // Return specific error for missing fields
      if (!line1) return this.createError({ path: `${this.path}.line1`, message: 'Line 1 is required when any address field is filled' });
      if (!city) return this.createError({ path: `${this.path}.city`, message: 'City is required when any address field is filled' });
      if (!state) return this.createError({ path: `${this.path}.state`, message: 'State is required when any address field is filled' });
      if (!zip) return this.createError({ path: `${this.path}.zip`, message: 'Zip code is required when any address field is filled' });
    }
    
    return true;
  }),
  deliveryEmailAddress: yup.string().email('Invalid email').test('is-email', 'Invalid email format', function(value) {
    if(!value) return true;
    return isEmail(value);
  }),
  shippingDocsEmail:  yup.string().trim().test('valid-emails', 'Enter valid email', value => {
    if (!value) return true;
    const emails = value.split(',');
    const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
    return isValid;
  }),
  addressNickName: yup.string(),
  dates: yup.array().of(yup.mixed()).optional(),
  resaleCertificateList: yup.array()
    .of(
      yup.object()
        .shape({
          resaleCertFile: yup.mixed(),
          cerificate_url_s3: yup.string().default(null).nullable(),
          state_id: yup.number().nullable()
            .when("cerificate_url_s3", {
              is: (s3Url: any) => s3Url?.trim()?.length > 0,
              then: (s) => s.required("Required")
            })
            .transform((value) => {
              if (isNaN(value)) {
                return null
              }
              return value;
            }),
          expiration_date: yup.string()
            .when("cerificate_url_s3", {
              is: (s3Url: any) => s3Url?.trim()?.length > 0,
              then: (s) => s.required("Required")
            }),
          uploadCertProgress: yup.boolean().default(false).nullable(),
          status: yup.string().nullable(),
          id: yup.string().nullable(),
          is_deletable: yup.string().nullable(),

        })
    ),

  creditLimit: yup.number().min(0, 'Credit limit must be greater than 0'),
  outstanding: yup.number().min(0, 'Outstanding must be greater than 0'),
  available: yup.number().min(0, 'Available must be greater than 0'),
  accountName: yup.string(),
  bankRoutingNumber: yup.string(),
  bankAccountNumber: yup.string(),

  cardType: yup.string(),
  cardNumberLast4Digits: yup.string(),
  cardExpiry: yup.string(),
  cardEmailId: yup.string(),
  cardFirstName: yup.string(),
  cardLastName: yup.string(),
  billingZipCode: yup.string().min(5,'Zip is not valid'),

  // BNPL
  bnplAvailable: yup.boolean(),
  net30CheckBox: yup.string(),
  einNumber: yup.string().trim().test("isRequired", "Ein Number is not valid", function(value) {
    if(!/^x{5}\d{4}$|^\d{2}-\d{7}$/.test(value)){
      return false
    }
    return !!value;
  }),
  dnBNumber: yup.string().test("isRequired", "D&B Number is not valid", function (value) {
    if(!/^x{5}\d{4}$|^\d{9}$/.test(value)){
      return false
    }
    return !!value;
  }),
  creditLine: yup.string().test("isRequired", "Credit Line is not valid", function (value) {
    if (value) {
      return +value > 0;
    } else {
      return false;
    }
  }),
  requestedCreditLimit: yup.string(),
  balanceCreditLimit: yup.string(),
  requestedIncreaseCredit: yup.string(),
  availableBalance: yup.string(),
  outstandingAmount: yup.string(),
  achCheckBox: yup.string(),
  bankName: yup.string(),
  routingNo: yup.string(),
  accountNo: yup.string(),
  requestCreditLine: yup.string(),
  refAccountNo: yup.string(),
  refRoutingNo: yup.string(),

});

export type SettingSchema = yup.InferType<typeof settingSchema>; 