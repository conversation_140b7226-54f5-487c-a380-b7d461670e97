.tabContent {
  padding: 20px 40px 0px 48px;
  background-color: #1a1b1f;
  position: relative;
  min-height: 625px;
  box-shadow: 0 -16px 15.1px -11px rgba(0, 0, 0, 0.6);
  border-style: solid;
  border-width: 1px;
  border-image-source: linear-gradient(180deg, #fff -70%, #1a1b21 22%);
  border-image-slice: 1;
  border-radius: 0px 0px 20px 20px;
  &.userTabContent,
  &.tabContentShipment,
  &.paymentTabContent {
    padding: 20px 5px 0px 48px;
  }
  .scrollerContainer{
    overflow: auto;
    max-height: calc(100vh - 250px);
    padding-right: 27px;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background-color: #8b91a6;
    }
  }

  &.paymentTabContent {
    padding-top: 20px;
  }
  &.userTabContent{
    padding: 20px 40px 15px 48px;
  }
}

.formEmpty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #808080;
  font-style: italic;
}

.formContainer {
  display: flex;
  flex-direction: column;
  max-height: 100%;
  overflow-y: auto;
}

.formGroupInput {
  display: flex;
  height: 56px;
  display: flex;
  justify-content: flex-start;
  border-bottom: solid 1px rgba(255, 255, 255, 0.07);

  &.bdrBtm0 {
    border-bottom: 0px;
  }

  &.stateSelectionGroup {
    height: auto;
    min-height: 220px;
    align-items: flex-start;
    padding: 8px 0 0 0;
    display: none; // Hide for temporary

    .col1 {
      &:first-child {
        align-items: flex-start;
        padding-top: 4px;
      }

      &:nth-child(2) {
        align-items: flex-start;
      }
    }
  }

  label {
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: left;
    color: rgba(255, 255, 255, 0.4);
    text-transform: uppercase;
  }

  // .focusLbl {
  //   color: #fff;
  // }

  .col1 {
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;

    &:nth-child(2) {
      flex: 0 365px;
    }
  }

  .bgAutoComplete {
    width: 100%;
    border-radius: 10px;
    background: url(../../../../assets/New-images/Create-Account/company-name-dropdown.svg)
      no-repeat;
    background-position: bottom right;
    background-size: contain;
    height: 157px;
    display: flex;
    align-items: baseline;
    position: relative;
    top: -3px;
    z-index: 999;

    .companyNameInput.companyNameInput {
      background: transparent;
      height: 45px;
      top: 8px;

      input {
        background: transparent;
        font-size: 14px;
      }
    }
  }

  .companyNameInput {
    width: 100%;
    position: relative;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 6px 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    border: 0px;

    &:focus-within {
      background: url(../../../../assets/New-images/Create-Account/input-active.svg)
        no-repeat;
      background-position: bottom right;
      background-size: cover;
    }

    input {
      width: fit-content;
      background-color: transparent;
      border: 0px;
      font-family: Inter;
      font-size: 18px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.72px;
      text-align: left;
      color: #fff;
      max-width: 35.5ch;

      &:focus {
        outline: none;
        color: #459fff;
      }
    }

    &:focus-within {
      outline: none;
    }
  }

  .inputWrapper {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .autoSuggestionText {
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.6px;
    text-align: left;
    color: rgba(69, 159, 255, 0.33);
    position: relative;
    top: 1px;
  }

  .inputCreateAccount {
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 6px 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0.72px;
    text-align: left;
    color: #fff;
    transition: background 0.1s;

    &.arBryzosCom{
      color: rgba(255, 255, 255, 0.4);
      &:focus{
         background: rgba(255, 255, 255, 0.04);
         color: rgba(255, 255, 255, 0.4);
      }
     }

    &.sendInvoiceEmailInput{
      text-overflow: ellipsis;
    }

    &.error {
      background: url(../../../../assets/New-images/Create-Account/error-input.svg)
        no-repeat;
      background-size: cover;
      box-shadow: none;

      &:focus {
        background-color: green;
        background: url(../../../../assets/New-images/Create-Account/error-input.svg)
          no-repeat;
        background-size: cover;
        color: #fff;
      }
    }

    &:focus {
      outline: none;
      color: #459fff;
      background: url(../../../../assets/New-images/Create-Account/input-active.svg)
        no-repeat;
      background-position: bottom right;
      background-size: cover;
    }

    &:disabled {
      cursor: not-allowed;
    }
  }

  .locationAdressPreviewContainer {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    width: 320px;
    line-height: 28px;
  }

  .changePassword {
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.4);
    cursor: pointer;
  }

  .confirmPasswordInput {
    width: 100%;
    position: relative;
    z-index: 12;

    &.focusPass {
      background: url(../../assets/New-images/Create-Account/password-active.svg)
        transparent no-repeat;
      background-size: contain;
      position: absolute;
      height: 100px;
      top: 7px;
      z-index: 9;

      .passwordInput {
        border-radius: 12px 12px 0px 0px;
        background: transparent;
        position: relative;
      }
    }

    &.focusPass2 {
      top: -45px;
      display: flex;
      align-items: flex-end;

      .passwordInput2 {
        border-radius: 0px 0px 12px 12px;
        position: relative;
        top: -5px;
      }
    }

    &.bgRemove {
      .passwordInput {
        background: transparent !important;
      }
    }
  }

  .inputOnboarding1 {
    width: 100%;
  }

  .passwordInput {
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    width: 100%;
    height: 40px;
    padding: 0px 16px;

    &:focus-within {
      background: rgba(255, 255, 255, 0.04);
    }

    input {
      background-color: transparent;
      padding: 0px;
    }
  }

  .passwordRequirements {
    position: absolute;
    top: -23px;
    left: 0px;
    width: 99%;
    height: 18px;
    display: flex;
    padding: 0px 20px;
    flex-direction: row;
    z-index: 999;
    justify-content: space-between;
    align-items: center;
    border-image-source: linear-gradient(
      to top,
      #fff 120%,
      rgba(26, 27, 32, 0) 85%
    );
    border-image-slice: 1;
    background-color: #1b1c21;
    font-family: Inter;
    font-size: 11px;
    font-weight: normal;
    line-height: 1.23;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.5);
    z-index: 1;
    box-shadow: inset 4px 4px 10.1px 0 #000;

    &.passwordRequirements2 {
      top: 28px;
    }
  }
}

.hide {
  pointer-events: none;
  cursor: default;
  .col1{
    label{
     color: rgba(255, 255, 255, 0.1);
    }
  }
}

.receivingHoursInput {
  height: 100px;
  padding: 8px 0px;

  .lblInput {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-right: solid 1px #000;

    .lblReceivingHours {
      font-size: 12px;
      padding-left: 6px;
    }
  }

  .inputSectionRecevingHours {
    width: 100%;
    height: 100%;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    label {
      cursor: pointer;

      &:hover {
        color: #fff;
      }
    }

    input[type='checkbox'] {
      display: none;
    }

    .daylbl1 {
      font-family: Syncopate;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: normal;
      text-align: center;
      color: #fff;
      text-transform: uppercase;
      margin-bottom: 4px;
    }
  }
}

.resaleCertContainer {
  width: 100%;
  height: 100%;
  min-height: 100px;

  .col1 {
    &:first-child {
      align-items: baseline;
      padding-top: 15.5px;
    }

    &:nth-child(2) {
      padding: 8px 0px;
    }
  }

  .resaleCertContainerHeaderContainer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    height: 100%;
  }
}

.resaleCertContainerInner {
  display: flex;
  flex-direction: column;
  column-gap: 8px;

  .lblrow {
    display: flex;
    column-gap: 5px;
    margin-bottom: 4px;
    justify-content: center;

    span {
      font-family: Syncopate;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: normal;
      text-align: center;
      color: #fff;

      &:nth-child(1) {
        width: 90px;
      }

      &:nth-child(2) {
        width: 65px;
      }

      &:nth-child(3) {
        width: 116px;
      }

      &:nth-child(4) {
        width: 90px;
      }
    }
  }

  .addAnotherCertBtn {
    margin-top: 8px;
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.5);

    &:focus {
      color: #32ff6c;
      outline: none;
    }
  }
}

.resaleCertContainerMain {
  display: flex;
  column-gap: 5px;
  margin-bottom: 5px;
  position: relative;

  .uploadCertCol {
    width: 90px;
  }

  .resaleCertDropdown {
    width: 65px;
  }

  .resaleCertDropdown1 {
    width: 116px;
  }

  .uploadCertBtnStatus {
    width: 90px;
    justify-content: center;
  }
}

.deleteCertLineBtn {
  svg {
    width: 14px;
    height: 14px;

    circle,
    line {
      stroke: #ff0000;
      fill: transparent;
    }
  }
}

.uploadCertCol {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 4px;

  .lbl {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: center;
    color: #fff;
  }

  .uploadFileBtn {
    height: 30px;
    align-self: stretch;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    padding: 8.5px 10px 0;
    border-radius: 8px;
    background-image: linear-gradient(
      to right,
      #0f0f14 -12%,
      rgba(57, 62, 71, 0.67) 32%,
      rgba(57, 62, 71, 0.72) 67%,
      #0f0f14 123%
    );
    font-family: Syncopate;
    font-size: 12px;
    font-weight: bold;
    font-style: normal;
    line-height: 1.26;
    letter-spacing: -0.48px;
    text-align: center;
    color: rgba(255, 255, 255, 0.2);
    text-transform: uppercase;

    &:focus {
      outline: solid 1px #71737f;
    }
  }

  .uploadFileInput {
    display: none;
  }

  .uploadIcon {
    height: 100%;
    &:focus{
      svg{
        path{
          fill:#32ff6c
        }
      }
    }
  }
}

.resaleCertDropdown {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 4px;
}

.uploadCertBtnStatus {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 4px;

  .lbl {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: center;
    color: #fff;
  }

  .uploadCertBtn {
    width: 90px;
    height: 30px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: linear-gradient(
      236.5deg,
      rgba(255, 119, 89, 0.1) 0%,
      rgba(255, 83, 45, 0.1) 100%
    );
    box-shadow: 0 -4px 4px 0 rgba(0, 0, 0, 0.8);

    &[disabled] {
      opacity: unset;
      background-image: linear-gradient(
        to right,
        #0f0f14 -12%,
        rgba(57, 62, 71, 0.67) 32%,
        rgba(57, 62, 71, 0.72) 67%,
        #0f0f14 123%
      );
      box-shadow: none;

      span {
        background-image: unset;
        color: rgba(255, 255, 255, 0.4);
        -webkit-text-fill-color: unset;
      }
    }

    span {
      background-image: linear-gradient(316deg, #ff7759 130%, #ff532d -17%);
      font-family: Syncopate;
      font-size: 12px;
      font-weight: bold;
      font-stretch: normal;
      line-height: 1.3;
      letter-spacing: -0.74px;
      text-align: left;
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-transform: uppercase;
    }
  }
}

.resaleFirstBottomDiv {
  position: absolute;
  left: -87%;
  display: flex;
  top: 15px;
  align-items: center;
  margin-bottom: 4px;
  width: 100%;

  .state1AddLine,
  .state1 {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.5);
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    width: 100%;
  }

  .stateLbl {
    flex: 0 65px;
  }

  .viewCert {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.5);
    margin-right: 3px;
    text-decoration: none;

    &:focus {
      color: #32ff6c;
      outline: none;
    }
  }

  .deleteCertBtn {
    display: inline-flex;
    margin-right: 6px;
    margin-left: 1px;
  }
}

.UploadCertStatus {
  font-family: Syncopate;
  font-size: 12px;
  font-weight: normal;
  font-style: normal;
  line-height: 1.26;
  letter-spacing: -0.48px;
  text-align: center;
  color: #fff;
  text-transform: uppercase;
}

.autocompleteContainer {
  position: relative;
  width: 100%;
}

.deliveryAddressInput {
  width: 100%;
  height: 40px;

  :global(.MuiOutlinedInput-root) {
    border-radius: 4px;
    background-color: #fff;

    &:hover,
    &.Mui-focused {
      .MuiOutlinedInput-notchedOutline {
        border-color: #3f51b5;
      }
    }
  }

  :global(.MuiOutlinedInput-input) {
    padding: 12px 14px;
    font-size: 14px;
  }

  :global(.MuiFormHelperText-root) {
    color: #d32f2f;
    margin-top: 4px;
    font-size: 12px;
  }
}

.autocompleteDropdown.autocompleteDropdown {
  max-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 8px;
  border-radius: 10px;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  background-color: rgba(128, 130, 140, 0.28);
  margin-top: 4px;

  ul {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background-color: rgba(139, 145, 166, 0.64);
    }

    li {
      margin-right: 3px;
      padding: 6px 16px;
      border-radius: 8px;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);
      background-color: transparent;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        color: #fff;
      }

      &[aria-selected='true'] {
        background-color: transparent !important;
        box-shadow: unset;
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2) !important;
          color: #fff;
        }
      }
    }
  }

  &.autocompleteDBA {
    ul {
      li {
        font-size: 16px;
        padding: 8px 16px;
      }
    }
  }
}

.addNewDestinationBtn {
  font-family: Syncopate;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-align: left;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s;
  z-index: 1;

  &:focus {
    color: #32ff6c;
  } 

  &:hover:not(:disabled) {
    background-color: rgba(63, 81, 181, 0.1);
  }

  &:disabled {
    border-color: #cccccc;
    color: #cccccc;
    cursor: not-allowed;
  }
}

.deliveryAddressContainer {
  padding: 0px 0px 8px 0px;
  height: auto;
  .col1 {
    &:first-child {
      align-items: baseline;
      padding: 8.5px 0px 0px 0px;
    }
  }
}

.toggleHeaderIcons {
  display: flex;
  gap: 30px;
  margin-right: 30px;

  .toggleIcon {
    width: 30px;
    height: 30px;
    background-color: rgba(69, 159, 255, 0.2);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Syncopate;
    font-weight: bold;
    font-size: 14px;
    color: #fff;
  }
}

.notificationCheckbox {
  width: 30px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Syncopate;
  font-weight: bold;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  &.textType {
    &.checked {
      background-color: #286e3a;
      color: #fff;

      &:hover {
        background-color: #2f8045;
      }
    }
  }

  &.emailType {
    &.checked {
      background-color: #e98a23;
      color: #fff;

      &:hover {
        background-color: #f59a33;
      }
    }
  }

  &.desktopType {
    &.checked {
      background-color: #3366cc;
      color: #fff;

      &:hover {
        background-color: #4477dd;
      }
    }
  }
}

.categoryHeader {
  display: flex;
  align-items: center;
  margin: 20px 0 5px;

  .categoryTitle {
    font-family: Syncopate;
    font-weight: bold;
    font-size: 20px;
    color: #fff;
    flex: 1;
  }
}

.notificationCategory {
  margin-bottom: 20px;
}

.notificationRow {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.07);

  &:last-child {
    border-bottom: none;
  }

  .notificationTitle {
    flex: 1;
    font-family: Syncopate;
    font-size: 14px;
    font-weight: normal;
    color: rgba(255, 255, 255, 0.8);
    padding-left: 15px;

    span {
      &:hover {
        color: #fff;
      }
    }
  }

  .notificationToggle {
    margin: 0 30px;
  }
}

.formActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 40px;

  .saveButton {
    background-color: #459fff;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 12px 30px;
    font-family: Syncopate;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;

    &:hover {
      background-color: #3a89e6;
    }
  }
}

.notificationsSection {
  margin-top: 30px;
}

.contactInfoSection {
  margin-bottom: 30px;
}

.toggleHeader {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 10px;
}

.toggleHeaderLabel {
  flex: 1;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;

  .methodText {
    font-weight: 500;
    color: #fff;

    .methodIcon {
      display: inline-block;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      background-color: rgba(69, 159, 255, 0.2);
      border-radius: 4px;
      margin: 0 2px;
      font-family: Syncopate;
      font-weight: bold;
      font-size: 12px;
    }
  }
}

.notificationsFormContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.notificationDisplayContainer {
  max-height: 489px;
  overflow-y: auto;
  margin-top: 20px;
}

.customeAddressPopup {
  .closeIcon {
    position: absolute;
    right: 20px;
    z-index: 1;
    top: 10px;
  }

  .dialogContent {
    border-radius: 20px;
    width: 100%;
    padding: 50px 40px;
    background: url(../../../../assets/New-images/Create-Account/dialogBG.svg)
      #0f0f14 no-repeat;
    background-size: cover;
    background-position: center;
  }

  .customAddressContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    row-gap: 20px;

    input {
      width: 100%;
    }
  }

  .zipInputContainer {
    display: flex;
    gap: 20px;

    .col1 {
      flex: 0 0 220px;
    }

    .col2,
    .col3 {
      flex: 1;
    }
  }
}

.locationAdressPreviewContainer {
  width: 100%;
  height: 100%;
}

.selectDropdown.selectDropdown {
  width: 100%;

  :global(.MuiSelect-select) {
    width: 100%;
    height: 40px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 20px;
    padding: 0 0 0 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    border: 1px solid transparent;
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #fff;

    // span {
    //     color: #616575;
    // }
  }

  &.selectState {
    :global(.MuiSelect-select) {
      height: 50px;
      font-size: 14px;
    }
  }

  :global(.MuiSelect-icon) {
    path{
      fill:rgba(255, 255, 255, 0.6);
      stroke-opacity: unset;
    }
    transform: none;
  }

  &:global(.Mui-focused) {
    :global(.MuiSelect-select) {
      border: solid 1px #71737f;
      background-color: rgba(255, 255, 255, 0.04);
    }
}

  :global(fieldset) {
    border-color: transparent !important;
  }
}

.Dropdownpaper.Dropdownpaper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 8px;
  padding: 4px;
  border-radius: 10px;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  background-color: rgba(128, 130, 140, 0.28);
  padding: 0px;
  margin-top: 4px;

  .muiMenuList {
    padding: 4px;

    li {
      padding: 6px 16px;
      border-radius: 8px;
      font-family: Inter;
      font-size: 18px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);
      background-color: transparent;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        color: #fff;
        font-weight: bold;
      }
    }
  }

  &.Dropdownpaper1 {
    width: auto;
    overflow: hidden;
    padding-right: 5px;
    padding-top: 4px;
    padding-bottom: 4px;

    ul {
      max-height: 220px;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 8px;
        background-color: rgba(139, 145, 166, 0.64);
      }

      li {
        font-size: 14px;
        margin-right: 4px;
      }
    }
  }

  &.resaleCertdropdown {
    width: auto;
    overflow: hidden;
    padding-right: 5px;
    padding-top: 4px;
    padding-bottom: 4px;

    ul {
      max-height: 220px;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 8px;
        background-color: rgba(139, 145, 166, 0.64);
      }

      li {
        font-size: 14px;
        margin-right: 4px;
      }
    }
  }

  &.receivingHoursTop {
    padding-right: 3px;
    border-radius: 6px 6px 0px 0px;

    ul {
      &::-webkit-scrollbar {
        width: 4px;
      }

      li {
        font-size: 12px;
        margin-right: 0px;
        padding: 6px 4px;
        border-radius: 4px;
      }
    }
  }

  &.receivingHoursBottom {
    padding-right: 3px;
    border-radius: 0px 0px 6px 6px;

    ul {
      &::-webkit-scrollbar {
        width: 4px;
      }

      li {
        font-size: 12px;
        margin-right: 0px;
        padding: 6px 4px;
        border-radius: 4px;
      }
    }
  }
}

.dropdownValue {
  width: 65px;
  height: 30px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.04);
  font-family: Inter;
  font-size: 12px;
  font-weight: 300;
  line-height: 1;
  letter-spacing: normal;
  text-align: left;
  color: #fff;

  &.dropdownValue1 {
    width: 116px;
  }
}

.changePassDialog {
  .dialogContent {
    background: url(../../../../assets/New-images/Change-Pass-BG.svg) no-repeat;
    background-size: cover;
    max-height: 91%;
    overflow: hidden;
    border-radius: 20px;
    width: 400px;
  }

  .closeIcon {
    position: absolute;
    top: 8px;
    right: 8px;
  }
}


.paymentTabContent {
  padding: 20px 40px 36px 40px;

  .paymentHeader {
    color: #fff;
  }

  .paymentHeaderText {
    font-family: Inter;
    font-size: 16px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #dbdcde;
  }

  .headerBtn {
    width: 192px;
    height: 30px;
    border-radius: 10px;
    background-image: linear-gradient(
      to right,
      #0f0f14 -12%,
      rgba(57, 62, 71, 0.67) 32%,
      rgba(57, 62, 71, 0.72) 67%,
      #0f0f14 123%
    );
    &:focus{
      outline: 1px solid #71737f;
    }
    span {
      font-family: Syncopate;
      font-size: 12px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.26;
      letter-spacing: -0.48px;
      text-align: center;
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      background-image: linear-gradient(205deg, #fff -14%, #999 47%);
    }
  }
}

.bnplStatusContainer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;

  .headerBtn {
    width: auto;
    text-transform: uppercase;
    padding: 8px 24px 7px;
  }

  .approvedStatus {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.3;
    letter-spacing: normal;
    text-align: left;
    color: #32ff6c;
    margin-left: auto;
    text-transform: uppercase;
    margin-bottom: 4px;
  }

  .pendingStatus {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.3;
    letter-spacing: normal;
    text-align: left;
    color: #ffb800;
    margin-left: auto;
    text-transform: uppercase;
    margin-bottom: 4px;
  }

  .pendingReviewStatus {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.3;
    letter-spacing: normal;
    text-align: right;
    color: #ffb800;
    margin-left: auto;
    text-transform: uppercase;
    flex: 0 200px;
  }
}

.pendingStatus {
  color: #71737f;
}

.stripePaymentGrid {
  column-gap: 12px;
}

.stripeElement {
  width: 100%;
  height: 40px;
  flex-grow: 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 0 12px 0 16px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);

  & > div {
    width: 100%;
  }

  /* Extra styling to ensure the iframe is visible */
  iframe {
    opacity: 1 !important;
    height: 24px !important;
    min-height: 24px !important;
    width: 100% !important;
  }
}

.creditLimitPopup {
  .formGroupInput {
    margin-bottom: 24px;
    border-bottom: 0px;

    .col1 {
      flex: 0 50%;

      label {
        font-size: 16px;
      }
    }
  }

  .headerBtn {
    width: 100%;
    height: 45px;

    span {
      font-size: 16px;
    }
  }
}


.net30TermsRequestCreditLine.net30TermsRequestCreditLine {
  display: flex;
  align-items: center;
  column-gap: 12px;

  input {
    flex: 1;
    height: 30px;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    border-radius: 10px;

    &::placeholder {
      color: rgba(255, 255, 255, 0.4);
    }
  }

  .headerBtn {
    flex: 0 120px;
  }
}

.uploadText {
  font-size: 9px;
  display: inline-block;
  animation: uploadingPulse 1s infinite ease-in-out;
}

@keyframes uploadingPulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.ErrorDialog {
  .dialogContent {
    max-width: 300px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 30px 34px 30px 34px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;

    p {
      margin-bottom: 20px;
    }

    .submitBtn {
      height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 10px 24px;
      border-radius: 4px;
      border: solid 0.5px #fff;
      background-color: transparent;
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      margin-top: 20px;
      transition: all 0.1s;

      &:hover,
      &:focus {
        background-color: #70ff00;
        border: solid 0.5px #70ff00;
        color: #000;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          border: solid 0.5px #fff;
          background-color: transparent;
          color: #fff;
        }
      }
    }

    .deleteBtnSection {
      display: flex;
      justify-content: center;
      button {
        display: inline-flex;
        &:last-child {
          margin-left: 16px;
        }
      }
    }
  }
}

.disabledCert {
  span {
    pointer-events: none;
    opacity: 0.5;
    &:nth-child(1) {
      pointer-events: unset;
      opacity: unset;
    }
  }
}


.impersonateIconStyle{
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 0 24px;
  color: #70ff00;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 18px;
  cursor: pointer;
  font-size: 14px;
} 
.whereCanYouFulfillOrders{
  height: 300px;
}