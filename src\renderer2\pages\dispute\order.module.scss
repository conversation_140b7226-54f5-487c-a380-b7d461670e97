.orderContent {
    border-radius: 0px 0px 10px 10px;
    margin: 0px auto;
    max-width: 800px;
    width: 100%;
    text-align: center;
    position: relative;
    padding-bottom: 12px;
    background-image: linear-gradient(102deg, #0f0f14 -8%, #393e47 238%);

    .orderSearch {
        display: flex;
        width: 100%;
        height: 79px;
        align-items: center;
        padding-right: 16px;

        input {
            border: 0;
            padding: 0px 16px;
            font-family: Noto Sans;
            font-size: 20px;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            background-color: transparent;

            &::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            &:focus-visible {
                outline: transparent;
            }
        }

        .btnOfOrderSearch {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 5px;

            button {
                font-family: Noto Sans;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.4;
                text-align: left;
                color: rgba(255, 255, 255);
                width: 80px;
                height: 21px;
                padding: 2px 0;
                border-radius: 2px;
                border: solid 0.5px rgba(255, 255, 255);
                background-color: transparent;
                text-align: center;
                opacity: 0.5;
                &:hover {
                    background-color: #70ff00;
                    opacity: unset;
                    color: #000;
                    border: 0.5px solid #000;
                    font-weight: normal;
                }
                &:focus {
                    border: 0.5px solid #70ff00;
                    opacity: unset;
                }
            }
            .activeBtn{
                background-color: #70ff00;
                opacity: unset;
                color: #000;
                border: 0.5px solid #000;
                font-weight: normal;
            }
        }
    }

    .orderHeading {
        height: 68px;
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 16px 12px;
        gap: 2px;
        box-shadow: 0 -16px 15.1px -11px rgba(0, 0, 0, 0.6);

        .bigHeading {
            font-family: Noto Sans;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            display: flex;
            gap: 4px;

            .aTag {
                font-family: Noto Sans;
                font-size: 14px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: rgba(255, 255, 255);
                padding: 1px 6px;
                border-radius: 2.8px;
                border: solid 0.5px rgba(255, 255, 255);
                background-color: rgba(0, 0, 0, 0.5);
                opacity: 0.5;
            }
        }

        .smallHeading {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: 300;
            line-height: 1.4;
            text-align: left;
            color: #fff;
        }
    }

    .listOfOrder {
        padding-right: 4px;

        ul {
            list-style: none;
            padding: 0px 4px 0px 12px;
            overflow-y: auto;
            height: 400px;

            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }

            &::-webkit-scrollbar-thumb {
                background:
                    #9da2b2;
                border-radius: 50px;
            }

            &::-webkit-scrollbar-track {
                background: transparent;
            }
            .liOfOrder {
                min-height: 105px;
                padding: 16px 12px;
                width: 100%;
                display: flex;
                margin-top: 4px;
                border-radius: 4px;
                border: 1px solid transparent;
                cursor: pointer;
                background: transparent;
                color: #fff;

                &:hover,
                &:focus {
                    border: solid 1px #70ff00;
                    outline: none;
                }

                div {
                    color: #fff;
                    &:nth-child(1) {
                        width: 24px;
                    }

                    &:nth-child(2) {
                        width: 100%;
                        padding-left: 5px;
                        text-align: left;

                        .firstLine {
                            font-family: Noto Sans;
                            font-size: 18px;
                            font-weight: 500;
                            line-height: 1.4;
                            text-align: left;
                        }

                        .secondLine {
                            font-family: Noto Sans;
                            font-size: 16px;
                            font-weight: 300;
                            line-height: 1.4;
                            letter-spacing: normal;
                            text-align: left;

                            span {
                                letter-spacing: 2.4px;
                            }
                        }
                    }

                    &:nth-child(3) {
                        display: flex;
                        width: 100%;
                        justify-content: right;

                        .firstTdText {
                            font-family: Noto Sans;
                            font-size: 14px;
                            font-weight: 300;
                            line-height: 1.4;
                            text-align: left;
                        }

                        .dollerText {
                            font-family: Noto Sans;
                            font-size: 14px;
                            line-height: 1.4;
                            text-align: right;
                            padding-left: 12px;
                        }

                        .btnOfAHText {
                            padding-top: 6px;
                            font-family: Noto Sans;
                            font-size: 14px;
                            font-weight: 300;
                            font-stretch: normal;
                            font-style: normal;
                            line-height: 1.4;
                            letter-spacing: normal;
                            text-align: right;

                            .accpetRejectBtn {
                                font-size: 12px;      
                                font-weight: 300px;                        
                                color: rgba(255, 255, 255);
                                padding: 2px 5px;
                                border-radius: 2.8px;
                                border: solid 0.5px rgba(255, 255, 255);
                                background-color: rgba(0, 0, 0, 0.5);
                                opacity: 0.5;
                                margin-left: 6px;
                                &:hover,
                                &:focus{
                                    background-color: #70ff00;
                                    opacity: unset;
                                    color: #000;
                                    border: solid 1px #000;
                                    font-size: 12px;
                                    font-weight: 600;
                                }
                                &:disabled{                      
                                    color: rgba(255, 255, 255);
                                    border: solid 0.5px rgba(255, 255, 255);
                                    background-color: rgba(0, 0, 0, 0.5);
                                    opacity: 0.2;
                                    cursor: not-allowed;
                                    font-weight: 300;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .btnSection {
        border-top: 1px solid #000;
        padding-left: 20px;
        padding-top: 10px;
        margin-top: 20px;

        div {
            &:nth-child(1) {
                text-align: left;
            }
        }

        .termsAndPatent {
            position: relative;
    
            .TermsandConditions {
                font-family: Noto Sans;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.6;
                text-align: center;
                color: #fff;
                cursor: pointer;
            }
    
            .patentPendingText {
                font-family: Noto Sans;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.6;
                text-align: center;
                color: #fff;
    
    
            }
        }

        .backBtn {
            font-family: Noto Sans;
            font-size: 18px;
            font-weight: normal;
            line-height: 1.6;
            color: #fff;
            border: 0px;
            outline: none;
            background-color: transparent;
            text-align: left;
            cursor: pointer;
            padding-left: 10px;
            &:hover,
            &:focus{
                color: #b3b3b3;
            }
        }
    }
}
.ErrorDialog {
    .dialogContent {
      max-width: 330px;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      padding: 30px 34px 30px 34px;
      object-fit: contain;
      border-radius: 10px;
      -webkit-backdrop-filter: blur(24px);
      backdrop-filter: blur(24px);
      box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
      background-color: rgba(0, 0, 0, 0.72);
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.6;
      text-align: center;
      color: #fff;
      p {
        margin-bottom: 20px;
      }
      .submitBtn {
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 10px 24px;
        border-radius: 4px;
        border: solid 0.5px #fff;
        background-color: transparent;
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        text-align: center;
        color: #fff;
        margin-top: 20px;
        transition: all 0.1s;
  
        &:hover {
          background-color: #70ff00;
          border: solid 0.5px #70ff00;
          color: #000;
        }
      }
  
    }
}
