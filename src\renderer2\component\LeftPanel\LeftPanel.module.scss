.menuParentContainer{
    width: 346px;
    display: flex;
    position: relative;
    overflow: hidden;
    padding: 24px;
    .slideInContainer{
        flex-grow: 1;
        position: relative;
        transition: left var(--animation-duration) ease-in-out;

        .wrapperOverlay{
            right: -6px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
        .menuContainer {
            height: 100%;
            color: #fff;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            inset: 0;
            &.sideMenuShorter{
                background: url(../../assets/New-images/Side-Menu-Shorter.svg) #191a20 no-repeat;
            }

            &.sideMenuShorterSeller{
                background: url(../../assets/New-images/Side-Menu-Shorter-Seller.svg) #191a20 no-repeat;
            }

            &.sideMenuTaller{
                background: url(../../assets/New-images/Side-Menu-Taller.svg) #191a20 no-repeat;
            }
            &.sideMenuTallerSeller{
                background: url(../../assets/New-images/Side-Menu-Taller-Seller.svg) #191a20 no-repeat;
            }

            .sideMenu {
                width: 100%;
                height: 100%;
            
                .sideMenuRow1Tall {
                    width: 100%;
                    height: 154px;
                    padding: 30px 24px;
                    position: relative;
                    overflow: hidden;
                    
                    .sideMenuHeader {
                        display: flex;
                        gap: 4.6px;
            
                        .sideMenuHeaderIcon {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            width: 42px;
                            height: 42px;
                            overflow: hidden;
                            padding-top: 9px;

                            &.disabledBtn{
                                pointer-events: none;
                                opacity: 0.5;
                            }
        
                            &:hover {
                                .iconDivImg1 {
                                    display: none;
                                }
            
                                .iconDivImg2 {
                                    display: block;
                                }
                            }
                            &:focus-visible {
                                .iconDivImg1 {
                                    display: none;
                                }
            
                                .iconDivImg2 {
                                    display: block;
                                }
                            }
            
                            .iconDivImg2 {
                                display: none;
                            }
            
                        }
                        .pinFocus {
                            .iconDivImg1 {
                                display: none;
                            }
            
                            .iconDivImg2 {
                                display: block;
                            }
                        }
                        .active {
                            .iconDivImg1 {
                                display: none;
                            }
            
                            .iconDivImg2 {
                                display: block;
                            }
                        }
                    }
            
                    .searchBoxDiv {
                        display: flex;
                        justify-content: center;
                        margin-top: 12px;
            
                        .searchBox {
                            width: 273px;
                            height: 40px;
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-start;
                            align-items: center;
                            gap: 16px;
                            border-radius: 10px;
                            background-color: rgba(255, 255, 255, 0.04);
                            transition: background-color 0.1s ease-in-out;
            
                            &:focus-within {
                                background: url('../../assets/New-images/SearchInputActive.svg') rgba(255, 255, 255, 0.04) no-repeat bottom;
                                background-size: cover;
                            }
            
                            input {
                                background-color: transparent;
                                border: none;
                                width: 100%;
                                height: 100%;
                                padding: 11px 12px 11px 0;
                                font-family: 'Inter', sans-serif;
                                font-size: 15px;
                                font-weight: normal;
                                letter-spacing: 0.6px;
                                color: #1fbbfe;
                                transition: all 0.1s ease-in-out;
            
                                &::placeholder {
                                    color: #616575;
                                }
            
                                &:focus {
                                    outline: none;
                                }
                            }
            
                            svg {
                                margin-left: 16px;
                                width: 32px;
                            }
                        }
                    }
            
                }

                .sideMenuRow1Small {
                    width: 100%;
                    height: 121px;
                    padding: 16px 24px;
                    position: relative;
                    overflow: hidden;
                  
                    .sideMenuHeader {
                        display: flex;
                        gap: 11.25px;
            
                        .sideMenuHeaderIcon {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            width: 36px;
                            height: 36px;
                            overflow: hidden;
                            padding-top: 4px;

                             &.disabledBtn{
                                cursor: not-allowed;
                                opacity: 0.5;
                            }

                            .homeActiveIcon{
                                cursor: default;
                            }
        
                            svg {
                                position: relative;
                                top: 2px;
                            }
            
                            &:hover {
                                .iconDivImg1 {
                                    display: none;
                                }
            
                                .iconDivImg2 {
                                    display: block;
                                    cursor: pointer;
                                }
                            }
                            &:focus-visible {
                                .iconDivImg1 {
                                    display: none;
                                }
            
                                .iconDivImg2 {
                                    display: block;
                                }
                            }
            
                            .iconDivImg2 {
                                display: none;
                            }
            
                        }
                        .pinFocus {
                            .iconDivImg1 {
                                display: none;
                            }
            
                            .iconDivImg2 {
                                display: block;
                            }
                        }
                        .active {
                            .iconDivImg1 {
                                display: none;
                            }
            
                            .iconDivImg2 {
                                display: block;
                            }
                        }
                    }
            
                    .searchBoxDiv {
                        display: flex;
                        justify-content: center;
                        margin-top: 12px;
            
                        .searchBox {
                            width: 273px;
                            height: 40px;
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-start;
                            align-items: center;
                            gap: 16px;
                            border-radius: 10px;
                            background-color: rgba(255, 255, 255, 0.04);
                            transition: background-color 0.1s ease-in-out;
            
                            &:focus-within {
                                background: url('../../assets/New-images/SearchInputActive.svg') rgba(255, 255, 255, 0.04) no-repeat bottom;
                                background-size: cover;
                            }
            
                            input {
                                background-color: transparent;
                                border: none;
                                width: 100%;
                                height: 100%;
                                padding: 11px 12px 11px 0;
                                font-family: 'Inter', sans-serif;
                                font-size: 15px;
                                font-weight: normal;
                                letter-spacing: 0.6px;
                                color: #1fbbfe;
                                transition: all 0.1s ease-in-out;
            
                                &::placeholder {
                                    color: #616575;
                                }
            
                                &:focus {
                                    outline: none;
                                }
                            }
            
                            svg {
                                margin-left: 16px;
                                width: 32px;
                            }
                        }
                    }
                    .sideMenuBgEllips {
                        position: absolute;
                        top: -37px;
                        left: 25px;
                        width: 100px;
                        height: 38px;
                        filter: blur(55px);
                        background-image: conic-gradient(from 0.32turn, var(--W--01), rgba(151, 134, 255, 0.59) 0.99turn, var(--W--01) 0.09turn, rgba(151, 134, 255, 0.1) 0.53turn, var(--W--01) 0.8turn, rgba(151, 134, 255, 0) 0.31turn, var(--W--01)), linear-gradient(348deg, var(--neutral-light) 210%, var(--neutral-light-transparent) 165%), linear-gradient(to bottom, rgba(151, 134, 255, 0), var(--secondary-color));
                      }
                      
                      .sideMenuBgEllips1 {
                        position: absolute;
                        bottom: -60px;
                        right: 23px;
                        width: 78px;
                        height: 32.5px;
                        filter: blur(26px);
                        background-image: conic-gradient(from 0.42turn, var(--W--01), rgba(151, 134, 255, 0.37) 0.89turn, var(--W--01) 0.09turn, rgba(151, 134, 255, 0.02) 0.83turn, var(--W--01) 0.51turn, var(--tertiary-color) 0.36turn, var(--W--01)), linear-gradient(274deg, var(--neutral-light) 210%, var(--neutral-light-transparent) 165%), linear-gradient(to bottom, rgba(151, 134, 255, 0.19), var(--secondary-color));
                      }
            
                }
            
                .sideMenuRow2 {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    text-align: center;
                    gap: 8px;
                    padding: 16px 0px;
                    font-family: Syncopate;
                    font-weight: normal;
            
                    .freeTrialText {
                        font-size: 10px;
                        line-height: 1.2;
                        color: #32ff6c;
                    }
            
                    .subscribeButton {
                        button {
                            border-radius: 10px;
                            border: solid 0.5px #fff;
                            width: 249px;
                            height: 37px;
                            padding: 11px 12px 8px 12px;
                            font-family: Syncopate;
                            font-size: 16px;
                            font-weight: bold;
                            line-height: 1.15;
                            letter-spacing: 2.88px;
                            text-align: center;
                            color: rgba(255, 255, 255, 0.9);
                        }
                    }
            
                    .learnMoreText {
                        color: #ffc44f;
                        line-height: 1.2;
                        font-size: 10px;
                    }
                }
            
                .sideMenuRow3 {
                    padding: 20px 6px 20px 20px;
                    height: calc(100% - 273px);

                    
                    &.subscribeScroll{
                      height: calc(100% - 170px);
                    }

                    &.sideMenuRow3Seller {
                        height: calc(100% - 128px);
                    }
            
                    .sideMenu3Scroller {
                        max-height: calc(100% - 20px);
                        overflow: auto;
            
                        &::-webkit-scrollbar {
                            width: 5px;
                            height: 6px;
                        }
            
                        &::-webkit-scrollbar-thumb {
                            background:#ffffff51;
                            border-radius: 50px;
                        }
            
                        &::-webkit-scrollbar-track {
                            background: transparent;
                        }
                    }
                    .savedBomScroller.savedBomScroller{
                        height: 100%;
                    }
            
            
                    .sideMenuDiv {
                        border-radius: 10px;
                        background-color: rgba(255, 255, 255, 0.04);
                        display: flex;
                        flex-direction: column;
                        margin-bottom: 20px;
                        margin-right: 9px;

                        &:last-child{
                            margin-bottom: 0;
                        }
            
                        .lineBackground {
                            height: 34px;
                            align-self: stretch;
                            flex-grow: 0;
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-start;
                            align-items: center;
                            gap: 8px;
                            padding: 12px 16px;
                            border-bottom: solid 1px #191a20;
                            font-family: Inter;
                            font-size: 14px;
                            line-height: 1.15;
                            letter-spacing: 0.98px;
                            color: #b8b7d4;
                            &:hover {
                                color: #fff;
                            }
                            &:focus-visible {
                                color: #fff;
                            }
                        }
                        .lineBackgroundActive{
                            color: #1D1D23;
                            background: rgb(255 255 255 / 65%);
                            &:hover {
                                color: #1D1D23;
                            }
                            &:focus-visible {
                                color: #1D1D23;
                            }
                        }

                        .impersonateUserDisabled{
                            color: #707070;
                            cursor: not-allowed;
                            display: none;
                        }
            
                        .firstLine {
                            font-family: Syncopate;
                            font-size: 15px;
                            font-weight: bold;
                            font-stretch: normal;
                            font-style: normal;
                            line-height: 1.15;
                            letter-spacing: 0.98px;
                            text-align: center;
                            color: #fff;
                            height: 40px;
                        }
            
                        .lastLine {
                            border: none;
                            border-bottom-left-radius: 10px;
                            border-bottom-right-radius: 10px;
                        }
                    }
                }

                .impersonatedUserScroll.impersonatedUserScroll {
             
                    .sideMenu3Scroller.sideMenu3Scroller {
                        max-height: calc(100% - 100px);
                    }

                }
            }

            .sideMenuRow4 {
                padding-left: 21px;
                padding-bottom: 16px;
                position: fixed;
                bottom: 24px;
        
                button {
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: 300;
                    line-height: 1.15;
                    letter-spacing: 0.84px;
                    text-align: left;
                    color: rgba(255, 255, 255, 0.25);
                    transition: all 0.1s;
        
                    &:hover {
                        color: #fff;
                    }
        
                    &:focus-visible {
                        color: #fff;
                    }
                }
            }
        }
    }
    .slideIn{
        left: 0;
    }
    .slideOut{
        left: 351px;
    }

    a, button, input, [tabindex]:not([tabindex="-1"]) {
        &:focus {
            outline: none;
        }
    }

    * {
        &:focus-visible {
            outline: none;
        }
    }
}

.openLeftPanel {
    animation: fadeIn var(--animation-duration) linear;
    @keyframes fadeIn {
        0% {
            opacity: 0;
            z-index: -1;
        }
        10%{
            opacity: 0.1;
        }
        50%{
            opacity: 0.2;
        }
        85%{
            opacity: 0.5;
        }
        100% {
            opacity: 1;
            z-index: 1;
        }
    }
    // opacity: 1;
}
.closeMenu {
    animation: fadeOut var(--animation-duration) linear;
    @keyframes fadeOut {
        0% {
            opacity: 1;
            z-index: -1;
        }
        15%{
            opacity: 0.5;
        }
        50%{
            opacity: 0.2;
        }
        85%{
            opacity: 0.1;
        }
        100% {
            opacity: 0; 
        }
    }
    opacity: 0;
}


.pinFocus.pinFocus {
    .iconDivImg1 {
        display: none;
    }

    .iconDivImg2.iconDivImg2 {
        display: block;
    }
}

.impersonatedUserMain{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    row-gap: 4px;
    padding: 20px 6px 0px 20px;
    width: 320px;
    .impersonatedUserLabel{
            font-family: Syncopate;
            font-size: 14px;
            font-weight: bold;
            line-height: 1.15;
            letter-spacing: 0.98px;
            text-align: center;
            color: #fff;
    }
    .impersonatedUserName{
            font-family: Inter;
            font-size: 14px;
            line-height: 1.15;
            letter-spacing: 0.98px;
            color: #fff;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 96%;
           
            span{
             color: #b8b7d4;
              margin-top: 3px;
            display: block;
            }
    }
}
