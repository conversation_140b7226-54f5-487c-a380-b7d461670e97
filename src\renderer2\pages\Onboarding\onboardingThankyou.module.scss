.thankyouBox{
    background: url(../../assets/New-images/AppBG.svg) no-repeat;
    .thankyouBoxContent{
        padding: 85px 135px;

        .title {
            font-family: Syncopate;
            font-size: 26.3px;
            font-weight: bold;
            font-stretch: normal;
            line-height: 1.4;
            letter-spacing: -1.05px;
            text-align: center;
            color: #fff;
            text-transform: uppercase;
            margin-bottom: 20px;
          }

          .thnkPara{
            font-family: Inter;
            font-size: 18.4px;
            font-weight: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
          }
    }

    .btnFooterLogin{
        width: 100%;
        height: 120px;
        padding: 0 28.1px 0 24px;
        box-shadow: 0 5.5px 4.4px -3px rgba(0, 0, 0, 0.91);
        background: url(../../assets/New-images/Create-Account/footerCreateAccount.svg) no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        .loginBtn {
            width: 532px;
            height: 53px;
            display: flex;
            flex-direction: row;
            justify-content:center;
            align-items: center;
            border-radius: 13.2px;
            box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
            background: linear-gradient(236.5deg, rgba(255, 119, 89, 0.1) 0%, rgba(255, 83, 45, 0.1) 100%);
            position: relative;
            z-index: 9;
            span{
                background-image: linear-gradient(316deg, #ff7759 130%, #ff532d -17%);
                font-family: Syncopate;
                font-size: 18.5px;
                font-weight: bold;
                font-stretch: normal;
                line-height: 1.3;
                letter-spacing: -0.74px;
                text-align: left;
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                text-transform: uppercase;
            }
          }
    }
}