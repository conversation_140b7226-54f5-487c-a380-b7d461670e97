.createAccountMain {
    background: url(../../assets/New-images/AppBG.svg) no-repeat;
    .createAccountForm {
        padding: 22px 48px 30px 48px;
        position: relative;
    }

    .formGroupInput {
        display: flex;
        height: 52px;
        display: flex;
        justify-content: flex-start;
        border-bottom: solid 1px rgba(255, 255, 255, 0.07);

        &.bdrBtm0 {
            border-bottom: 0px;
        }

        label {
            font-family: Syncopate;
            font-size: 18.2px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.73px;
            text-align: left;
            color: rgba(255, 255, 255, 0.4);
            text-transform: uppercase;
        }

        .focusLbl{
            color: #fff;
        }

        .radioGroupContainer {
            width: 100%;
            display: flex;
        }

        label.radioButtonLeft,
        label.radioButtonRight {
            flex: 1;
            font-family: Syncopate;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: center;
            color: #616575;
        }

        .col1 {
            flex: 1;
            display: flex;
            align-items: center;
            position: relative;
        }

        .bgAutoComplete {
            width: 100%;
            border-radius: 10px;
            background: url(../../assets/New-images/Create-Account/company-name-dropdown.svg) no-repeat;
            background-position: bottom right;
            background-size: contain;
            height: 157px;
            display: flex;
            align-items: baseline;
            position: relative;
            top: -3px;
            z-index: 999;

            .companyNameInput.companyNameInput {
                background: transparent;
                height: 45px;
                top:8px;

                input {
                    background: transparent;
                    font-size: 14px;
                }
            }
        }

        .companyNameInput {
            width: 100%;
            position: relative;
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            padding: 6px 16px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            border: 0px;          

            &:focus-within {
                background: url(../../assets/New-images/Create-Account/input-active.svg) no-repeat;
                background-position: bottom right;
                background-size: cover;
            }

            input {
                width: fit-content;
                background-color: transparent;
                border: 0px;
                font-family: Inter;
                font-size: 18px;
                font-weight: normal;
                line-height: normal;
                letter-spacing: 0.72px;
                text-align: left;
                color: #fff;
                max-width: 35.5ch;

                &:focus {
                    outline: none;
                    color: #459fff;
                }
            }

            &:focus-within {
                outline: none;
            }
        }

        .inputWrapper {
            display: flex;
            align-items: center;
            width:100%;
            
        }


        .autoSuggestionText {
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.6px;
            text-align: left;
            color: rgba(69, 159, 255, 0.33);
            position: relative;
            top:1px
        }

        .inputCreateAccount {
            width: 100%;
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: flex-start;
            padding: 6px 16px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 0.72px;
            text-align: left;
            color: #fff;
            transition: background 0.1s;

            &.error {
                background: url(../../assets/New-images/Create-Account/error-input.svg) no-repeat;
                background-size: cover;
                box-shadow: none;

                &:focus {
                    background: url(../../assets/New-images/Create-Account/error-input.svg) no-repeat;
                    background-size: cover;
                }
            }

            &:focus {
                outline: none;
                color: #459fff;
                background: url(../../assets/New-images/Create-Account/input-active.svg) no-repeat;
                background-position: bottom right;
                background-size: cover;
            }
        }

        .confirmPasswordInput {
            width: 100%;
            position: relative;
            z-index: 12;

            &.focusPass {
                background: url(../../assets/New-images/Create-Account/password-active.svg) transparent no-repeat;
                background-size: contain;
                position: absolute;
                height: 100px;
                top: 7px;
                z-index: 9;

                .passwordInput {
                    border-radius: 12px 12px 0px 0px;
                    background: transparent;
                    position: relative;
                }
            }


            &.focusPass2 {
                top: -45px;
                display: flex;
                align-items: flex-end;

                .passwordInput2 {
                    border-radius: 0px 0px 12px 12px;
                    position: relative;
                    top: -5px;
                }
            }

            &.bgRemove {
                .passwordInput {
                    background: transparent !important;
                }
            }

        }

        .inputOnboarding1 {
            width: 100%;
        }

        .passwordInput {
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            width: 100%;
            height: 40px;
            padding: 0px 16px;

            &:focus-within {
                background: rgba(255, 255, 255, 0.04);
            }


            input {
                background-color: transparent;
                padding: 0px;
            }
        }

        .passwordRequirements {
            position: absolute;
            top: -23px;
            left: 0px;
            width: 99%;
            height: 18px;
            display: flex;
            padding: 0px 20px;
            flex-direction: row;
            z-index: 999;
            justify-content: space-between;
            align-items: center;
            border-image-source: linear-gradient(to top, #fff 120%, rgba(26, 27, 32, 0) 85%);
            border-image-slice: 1;
            background-color: #1b1c21;
            font-family: Inter;
            font-size: 11px;
            font-weight: normal;
            line-height: 1.23;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.5);
            z-index: 1;
            box-shadow: inset 4px 4px 10.1px 0 #000;

            &.passwordRequirements2 {
                top: 28px
            }
        }


    }
}

.btnFooterTnc {
    width: 100%;
    height: 120px;
    padding: 0 40px 0 54px;
    box-shadow: 0 5.5px 4.4px -3px rgba(0, 0, 0, 0.91);
    background: url(../../assets/New-images/Create-Account/footerCreateAccount.svg) no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    justify-content: space-between;

    .btnBack {
        button {
            font-family: Inter;
            font-size: 15.6px;
            font-weight: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.4);
            display: flex;

            svg {
                margin-right: 10px;
            }
            span{
                padding-top: 4px;
            }
        }
    }

    .alreadyAccountLogin {
        font-family: Inter;
        font-size: 18px;
        font-weight: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.4);
        margin-left: auto;
        margin-right: 64px;

        span {
            color: #fff;
            cursor: pointer;
        }
    }

    .nextTncBtn {
        width: 160px;
        height: 52px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 12px;
        box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
        background: linear-gradient(236.5deg, rgba(255, 119, 89, 0.1) 0%, rgba(255, 83, 45, 0.1) 100%);

        &[disabled] {
            opacity: unset;
            background: rgba(255, 255, 255, 0.04);
            box-shadow: none;
            border-radius: 20px;
            span {
                background-image: unset;
                color: rgba(255, 255, 255, 0.4);
                -webkit-text-fill-color: unset
            }
        }

        span {
            background-image: linear-gradient(316deg, #ff7759 130%, #ff532d -17%);
            font-family: Syncopate;
            font-size: 18.5px;
            font-weight: bold;
            font-stretch: normal;
            line-height: 1.3;
            letter-spacing: -0.74px;
            text-align: left;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-transform: uppercase;
        }
    }

}

.autocompleteDescPanel {
    border-radius: 4px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    background: transparent;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    border-radius: 0px 0px 4px 4px;
    padding: 0px 0px 0px 16px;
    box-shadow: unset;

    .listAutoComletePanel.listAutoComletePanel {
        width: 100%;
        padding-right: 8px;
        margin: 2px 0px;
        padding: 0px 0px 0px 0px;
        max-height: 100px;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background:
                #9da2b2;
            border-radius: 50px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        span {
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.23;
            letter-spacing: normal;
            text-align: left;
            color: #fff;
            background-image: unset !important;
            padding-left: 0px;

            &[aria-selected="true"] {
                background-color: transparent !important;
                box-shadow: unset;
                color: #fff;
            }
        }
    }
}


.radioGroupContainer {
    display: flex;
    text-align: center;
    cursor: pointer;
    justify-content: center;
    position: relative;

    .chosseOneIcon {
        position: absolute;
        top: -19px;
    }

    .radioButton {
        height: 41px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        background-color: rgba(255, 255, 255, 0.04);
        &:not(.disableBidBuyNowBtn):hover {
            color: #459fff;
        }

        &.selected {
            font-family: Syncopate;
            font-size: 14px;
            font-weight: bold;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: #459fff;
        }

        &.buyerSelected {
            background: url(../../assets/New-images/Create-Account/buyer-active.svg);
            background-repeat: no-repeat;
            background-position: bottom left;
            color: #459fff !important;
            font-weight: bold !important;
            border-right: solid 1.5px #323336;
        }

        &.sellerSelected {
            background: url(../../assets/New-images/Create-Account/seller-active.svg);
            background-repeat: no-repeat;
            background-position: bottom right;
            border-left: none;
            color: #459fff !important;
            font-weight: bold !important;
            border-left: solid 1.5px #323336;
        }


    }

    .disableBidBuyNowBtn {
        cursor: not-allowed;
    }

    .radioButtonLeft {
        border-right: solid 2.5px rgba(1, 1, 1, 0.95);
        border-top-left-radius: 13px;
        border-bottom-left-radius: 13px;

        &:focus-visible {
            outline: none;

            &.radioButton {
                color: #459fff;
            }
        }
    }
    

    .radioButtonRight {
        border-left: solid 1px #1c1d23;
        border-top-right-radius: 13px;
        border-bottom-right-radius: 13px;

        &:focus-visible {
            outline: none;

            &.radioButton {
                color: #459fff;
            }
        }
    }

    .hiddenRadio {
        display: none;
    }
}

.passwordRequirements {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;

    .passwordRequirementItem {
        font-family: Noto Sans Display;
        font-size: 12px;
        font-weight: 300;
        line-height: 1.6;
        text-align: left;
    }

    .passwordRequirementItemActive {
        color: #32ff6c;
    }
}

.hiddenMeasure {
    visibility: hidden;
    position: absolute;
    white-space: pre;
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    height: 0;
    padding: 0;
    margin: 0;
  }
  .dialogContainer.dialogContainer {
    max-width: 474px;
    border-radius: 14px;
    background: url(../../assets/New-images/Create-Account/FREE-TRIAL-BG.svg) no-repeat;
    background-position: center;
    background-size: 100% 100%;
    box-shadow: none;
    padding: 4px 14px 11px 11px;
    

    .dialogHeader {
        padding: 20px 0px 18px 0px;
        height: 91px;
        font-family: Syncopate;
        font-size: 18.2px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: 4.92px;
        text-align: center;
        color: #fff;
        .dialogHeaderFreeTrial {
            font-weight: normal;
            line-height: 1.5;
            color: #ffc44f;
        }
    }

    .dialogContent {
        padding: 27px 36px 15px 37px;
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #fff;
        display: flex;
        flex-direction: column;
        gap: 18px;
        height: 270px;
        width: 100%;
        p {
            margin: 0;
        }
        .bold  {
            font-weight: bold;
        }   
    }

    .dialogFooter {
        border-image-slice: 1;
        padding: 20px;
        width: 100%;
        button {
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            width: 396px;
            height: 50px;
            font-family: Syncopate;
            font-size: 18.2px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.73px;
            text-align: center;
            color: rgba(255, 255, 255, 0.4);
            
        }
    }
  }
  .buttonNone {
    visibility: hidden
  }