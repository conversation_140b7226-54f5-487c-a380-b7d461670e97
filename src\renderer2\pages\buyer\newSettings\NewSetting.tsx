import React, { useEffect, useState } from 'react';
import styles from './NewSetting.module.scss';
import SearchHeader from '../../SearchHeader';
import TabNavigation from './tabs/TabNavigation';
import CompanyTab from './tabs/CompanyTab';
import UserTab from './tabs/UserTab';
import ShipmentsTab from './tabs/ShipmentsTab';
import PaymentsTab from './tabs/PaymentsTab';
import NotificationsTab from './tabs/NotificationsTab';
import axios from 'axios';
import { formatCurrency, useGlobalStore } from '@bryzos/giss-ui-library';
import useGetBuyingPreference from 'src/renderer2/hooks/useGetBuyingPreference';
import { yupResolver } from '@hookform/resolvers/yup';
import { set, useForm } from 'react-hook-form';
import { settingSchema } from './schemas';
import {
  formatPhoneNumberRemovingCountryCode,
  formatPhoneNumberWithHyphen,
} from 'src/renderer2/helper';
import {
  defaultResaleCertificateLine,
  RecevingHoursFrom,
  RecevingHoursTo,
} from 'src/renderer2/common';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import useGetDeliveryAddress from 'src/renderer2/hooks/useGetDeliveryAddress';
import useGetCompanyLists from 'src/renderer2/hooks/useGetCompanyLists';
import { useLocation } from 'react-router-dom';

const options = {
  fonts: [
    {
      cssSrc:
        'https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap',
    },
  ],
};

const NewSetting: React.FC<{
  mainWrapperRef: React.RefObject<HTMLDivElement>;
}> = ({ mainWrapperRef }) => {
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid },
    getFieldState,
  } = useForm({
    resolver: yupResolver(settingSchema),
    mode: 'onBlur',
  });

  const [activeTab, setActiveTab] = useState<string>('COMPANY');
  const { userData, showLoader, setShowLoader, referenceData , bryzosPayApprovalStatus }: any =
    useGlobalStore();
  const [stripePromise] = useState(
    loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY)
  );
  const [isBnplFilled, setIsBnplFilled] = useState<boolean>(false);
  const [isBnplApproved, setIsBnplApproved] = useState<string>('');
  const [bnplCreditStatus, setBnplCreditStatus] = useState<string>('');
  const [bnplStatus, setBnplStatus] = useState<string>('');
  const [creditStatus, setCreditStatus] = useState<string>('');
  const [deliveryAddresses, setDeliveryAddresses] = useState<any>([]);
  const [yourCompanyList, setYourCompanyList] = useState([]);
  const location = useLocation();

  const {
    data: buyingPreferenceData,
    isLoading: isBuyingPreferenceDataLoading,
    isFetching: isBuyingPreferenceDataFetching,
  } = useGetBuyingPreference();

  const {
    data: deliveryAddressData,
    isLoading: isDeliveryAddressLoading,
    isFetching: isDeliveryAddressFetching,
  } = useGetDeliveryAddress();

  const { data: companyListsData, isLoading: isCompanyListsDataLoading } =
    useGetCompanyLists();


    useEffect(() => {
      if(location?.state?.from === 'createPo'){
        setActiveTab('PAYMENTS');
      }
    }, [location.state?.from]);

  useEffect(() => {
    if (
      buyingPreferenceData &&
      !isBuyingPreferenceDataFetching &&
      !isBuyingPreferenceDataLoading
    ) {
      handleSetInitialValues(buyingPreferenceData);
    }
  }, [
    buyingPreferenceData,
    isBuyingPreferenceDataFetching,
    isBuyingPreferenceDataLoading,
  ]);

  useEffect(() => {
    if (
      deliveryAddressData &&
      !isDeliveryAddressFetching &&
      !isDeliveryAddressLoading
    ) {
      if (referenceData?.ref_states) {
        const modifiedRes = deliveryAddressData.map((address: any) => {
          address.stateCode = referenceData?.ref_states?.find(
            (state: any) => state.id === address.state_id
          )?.code;
          return address;
        });
        setDeliveryAddresses(modifiedRes);
      }
    }
  }, [
    deliveryAddressData,
    isDeliveryAddressFetching,
    isDeliveryAddressLoading,
  ]);

  useEffect(() => {
    if (watch('parentCompanyName') !== '' && companyListsData) {
      const companyData = companyListsData?.find(
        (companyData: any) =>
          companyData.company_name === watch('parentCompanyName')
      );
      setYourCompanyList(companyData?.client_company ?? []);
    }
  }, [watch('parentCompanyName'), companyListsData]);


  useEffect(() => {
    if (bryzosPayApprovalStatus) {
      handleBnplDetailsUpdate(bryzosPayApprovalStatus);
    }
  }, [bryzosPayApprovalStatus]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'COMPANY':
        return (
          <CompanyTab
            mainWrapperRef={mainWrapperRef}
            control={control}
            errors={errors}
            setError={setError}
            setValue={setValue}
            watch={watch}
            isDirty={isDirty}
            isValid={isValid}
            register={register}
            handleSubmit={handleSubmit}
            getValues={getValues}
            trigger={trigger}
            clearErrors={clearErrors}
            getFieldState={getFieldState}
            yourCompanyList={yourCompanyList}
            resetField={resetField}
            dirtyFields={dirtyFields}
            setActiveTab={setActiveTab}
          />
        );
      case 'USER':
        return (
          <UserTab
            mainWrapperRef={mainWrapperRef}
            control={control}
            errors={errors}
            setError={setError}
            setValue={setValue}
            watch={watch}
            isDirty={isDirty}
            isValid={isValid}
            register={register}
            handleSubmit={handleSubmit}
            trigger={trigger}
            resetField={resetField}
            dirtyFields={dirtyFields}
            setActiveTab={setActiveTab}
          />
        );
      case 'SHIPMENTS':
        return (
          <ShipmentsTab
            mainWrapperRef={mainWrapperRef}
            control={control}
            errors={errors}
            setError={setError}
            setValue={setValue}
            watch={watch}
            isDirty={isDirty}
            isValid={isValid}
            register={register}
            handleSubmit={handleSubmit}
            getValues={getValues}
            trigger={trigger}
            deliveryAddresses={deliveryAddresses}
            clearErrors={clearErrors}
            setDeliveryAddresses={setDeliveryAddresses}
            resetField={resetField}
            dirtyFields={dirtyFields}
            setActiveTab={setActiveTab}
          />
        );
      case 'PAYMENTS':
        return (
          <Elements stripe={stripePromise} options={options}>
            <PaymentsTab
              control={control}
              errors={errors}
              setError={setError}
              setValue={setValue}
              watch={watch}
              isDirty={isDirty}
              isValid={isValid}
              register={register}
              handleSubmit={handleSubmit}
              getValues={getValues}
              trigger={trigger}
              isBnplFilled={isBnplFilled}
              clearErrors={clearErrors}
              isBnplApproved={isBnplApproved}
              bnplCreditStatus={bnplCreditStatus}
              setIsBnplApproved={setIsBnplApproved}
              setBnplCreditStatus={setBnplCreditStatus}
              bnplStatus={bnplStatus}
              setBnplStatus={setBnplStatus}
            />
            ;
          </Elements>
        );

      default:
        return (
          <CompanyTab
            mainWrapperRef={mainWrapperRef}
            control={control}
            errors={errors}
            setError={setError}
            setValue={setValue}
            watch={watch}
            isDirty={isDirty}
            isValid={isValid}
            register={register}
            handleSubmit={handleSubmit}
            getValues={getValues}
            trigger={trigger}
            clearErrors={clearErrors}
            yourCompanyList={yourCompanyList}
            resetField={resetField}
            dirtyFields={dirtyFields}
          />
        );
    }
  };

  const handleSetInitialValues = async (buyerSettings: any) => {
    //company tab
    setValue('parentCompanyName', buyerSettings?.company_name || '');
    setValue('companyDBAName', buyerSettings?.client_company || '');
    setValue('companyType', buyerSettings?.company_type || '');
    setValue('companyAddress', {
      line1: buyerSettings?.company_address?.line1 || '',
      line2: buyerSettings?.company_address?.line2 || '',
      city: buyerSettings?.company_address?.city || '',
      state: buyerSettings?.company_address?.state_id || '',
      stateCode: buyerSettings?.company_address_state_code || '',
      zip: buyerSettings?.company_address?.zip || '',
    });

    setValue('buyerAddress', {
      line1: buyerSettings?.buyer_address?.line1 || '',
      line2: buyerSettings?.buyer_address?.line2 || '',
      city: buyerSettings?.buyer_address?.city || '',
      state: buyerSettings?.buyer_address?.state_id || '',
      zip: buyerSettings?.buyer_address?.zip || '',
      stateCode: buyerSettings?.buyer_address_state_code || '',
    });

    setValue('deliveryAddress', {
      line1: buyerSettings?.delivery_details?.line1 || '',
      line2: buyerSettings?.delivery_details?.line2 || '',
      city: buyerSettings?.delivery_details?.city || '',
      state: buyerSettings?.delivery_details?.state_id || '',
      stateCode: buyerSettings?.delivery_address_state_code || '',
      zip: buyerSettings?.delivery_details?.zip || '',
    });

    setValue('billingContactName', buyerSettings?.billing_contact_name || '');
    setValue('billingContactEmail', buyerSettings?.billing_email_id || '');
    setValue('sendInvoicesTo', buyerSettings?.send_invoices_to || '');
    setValue('sendRemittancesTo', '<EMAIL>');

    //usertab
    setValue('firstName', `${buyerSettings?.first_name} ${buyerSettings?.last_name}` || '');
    setValue('email', buyerSettings?.email_id || '');
    setValue(
      'phoneNumber',
      buyerSettings?.phone
        ? formatPhoneNumberWithHyphen(
            formatPhoneNumberRemovingCountryCode(buyerSettings?.phone)
          )
        : ''
    );
    setValue('searchZipcode', buyerSettings?.price_search_zip || '');
    setValue('stateSubscription', buyerSettings?.state_subscription || []);

    if (buyerSettings?.user_delivery_receiving_availability_details) {
      const weeks =
        buyerSettings?.user_delivery_receiving_availability_details.map(
          (day: any) => {
            day.receivingHrsFrom = [...RecevingHoursFrom];
            day.receivingHrsTo = [...RecevingHoursTo];
            return day;
          }
        );
      setValue('dates', weeks);
    }
    if (buyerSettings?.resale_certificate) {
      let resaleCertificateList = [defaultResaleCertificateLine];
      const resaleCertificate = buyerSettings.resale_certificate;
      if (resaleCertificate.length !== 0) {
        resaleCertificate.forEach((data: any, i: any) => {
          data.uploadCertProgress = false;
          resaleCertificateList[i] = data;
        });
      }
      setValue('resaleCertificateList', resaleCertificateList);
    }

    setValue(
      'addressNickName',
      buyerSettings?.delivery_details?.delivery_address_nickname || ''
    );
    setValue(
      'deliveryContactName',
      buyerSettings?.delivery_details?.delivery_contact_name || ''
    );
    setValue(
      'deliveryPhoneNumber',
      buyerSettings?.delivery_details?.delivery_phone
        ? formatPhoneNumberWithHyphen(
            formatPhoneNumberRemovingCountryCode(
              buyerSettings?.delivery_details?.delivery_phone
            )
          )
        : ''
    );
    setValue(
      'deliveryEmailAddress',
      buyerSettings?.delivery_details?.delivery_email_id || ''
    );
    setValue(
      'deliveryApptRequired',
      Boolean(buyerSettings?.delivery_details?.delivery_appt_required) || false
    );
    setValue('shippingDocsEmail', buyerSettings?.shipping_docs_to || '');

    if (buyerSettings.ach_credit) {
      setValue('bankName', buyerSettings.ach_credit.bank_name);
      setValue('routingNo', buyerSettings.ach_credit.routing_number);
      setValue('accountNo', buyerSettings.ach_credit.account_number);
    }

    if (buyerSettings.bnpl_settings) {
      let requestedCredit = parseFloat(
        buyerSettings.bnpl_settings.requested_credit_limit
      ).toString();

      if (buyerSettings.bnpl_settings.is_approved !== 0) {
        setValue('dnBNumber', buyerSettings.bnpl_settings.duns);
        setValue('einNumber', buyerSettings.bnpl_settings.ein_number);
        setValue('creditLine', requestedCredit);
      }
      setValue('requestedCreditLimit', requestedCredit);
      // setNet30ApplyStatus(true);

      let balanceCredit = formatCurrency(
        buyerSettings.bnpl_settings.balance_credit_limit
      );
      setValue('balanceCreditLimit', balanceCredit);
      if (buyerSettings.bnpl_settings.requested_increase_credit) {
        let requestedIncreaseCredit = formatCurrency(
          parseFloat(buyerSettings.bnpl_settings.requested_increase_credit)
        );
        setValue('requestedIncreaseCredit', requestedIncreaseCredit);
      }
      let availableBalanceCredit = formatCurrency(
        parseFloat(buyerSettings.bnpl_settings.balance_available_credit_limit)
      );
      setValue('availableBalance', availableBalanceCredit);
      let outstandingValue = '0';
      if (buyerSettings.bnpl_settings.outstanding_amount === null) {
        outstandingValue = '0';
      } else {
        outstandingValue = buyerSettings.bnpl_settings.outstanding_amount;
      }
      let outstandingBalanceAmount = formatCurrency(
        parseFloat(outstandingValue)
      );
      setValue('outstandingAmount', outstandingBalanceAmount);
      setIsBnplApproved(buyerSettings.bnpl_settings.is_approved);
      setBnplCreditStatus(buyerSettings.bnpl_settings.credit_status);
      setIsBnplFilled(true);
      setBnplStatus(buyerSettings.bnpl_settings.bnpl_status);
    } else {
      setIsBnplApproved('');
      setBnplCreditStatus('');
      setBnplStatus('');
      setIsBnplFilled(false);
    }

    if (buyerSettings?.credit_card) {
      setValue(
        'cardType',
        buyerSettings?.credit_card?.card_display_brand || ''
      );
      setValue(
        'cardNumberLast4Digits',
        buyerSettings?.credit_card?.card_number_last_four_digits || ''
      );
      setValue('cardExpiry', buyerSettings?.credit_card?.expiration_date || '');
      setValue('billingZipCode', buyerSettings?.credit_card?.zipcode || '');
      setValue(
        'cardEmailId',
        buyerSettings?.credit_card?.email_id || userData?.data?.email_id || ''
      );
      setValue(
        'cardFirstName',
        buyerSettings?.credit_card?.first_name ||
          userData?.data?.first_name ||
          ''
      );
      setValue(
        'cardLastName',
        buyerSettings?.credit_card?.last_name || userData?.data?.last_name || ''
      );
    }
    clearErrors();
    setShowLoader(false);
  };

  const handleBnplDetailsUpdate = (bnplSettings: any) => {
    if (bnplSettings) {
      let requestedCredit = parseFloat(
        bnplSettings.requested_credit_limit
      ).toString();

      if (bnplSettings.is_approved !== 0) {
        setValue('dnBNumber', bnplSettings.duns);
        setValue('einNumber', bnplSettings.ein_number);
        setValue('creditLine', requestedCredit);
      }else{
        setValue('dnBNumber', '');
        setValue('einNumber', '');
        setValue('creditLine', '');
      }
      setValue('requestedCreditLimit', requestedCredit);
      // setNet30ApplyStatus(true);

      let balanceCredit = formatCurrency(
        bnplSettings.balance_credit_limit
      );
      setValue('balanceCreditLimit', balanceCredit);
      if (bnplSettings.requested_increase_credit) {
        let requestedIncreaseCredit = formatCurrency(
          parseFloat(bnplSettings.requested_increase_credit)
        );
        setValue('requestedIncreaseCredit', requestedIncreaseCredit);
      }
      let availableBalanceCredit = formatCurrency(
        parseFloat(bnplSettings.balance_available_credit_limit)
      );
      setValue('availableBalance', availableBalanceCredit);
      let outstandingValue = '0';
      if (bnplSettings.outstanding_amount === null) {
        outstandingValue = '0';
      } else {
        outstandingValue = bnplSettings.outstanding_amount;
      }
      let outstandingBalanceAmount = formatCurrency(
        parseFloat(outstandingValue)
      );
      setValue('outstandingAmount', outstandingBalanceAmount);
      setIsBnplApproved(bnplSettings.is_approved);
      setBnplCreditStatus(bnplSettings.credit_status);
      setIsBnplFilled(true);
      setBnplStatus(bnplSettings.bnpl_status);
    } else {
      setIsBnplApproved('');
      setBnplCreditStatus('');
      setBnplStatus('');
      setIsBnplFilled(false);
    }
  }

  return (
    <div className={styles.newSetting}>
      <div>
        <SearchHeader />
      </div>
      <div className={styles.newSettingContent}>
        <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />
        {renderTabContent()}
      </div>
    </div>
  );
};

export default NewSetting;
