import { defineConfig, splitVendorChunkPlugin } from 'vite';
import react from '@vitejs/plugin-react';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import svgrPlugin from 'vite-plugin-svgr';
import progress from 'vite-plugin-progress';
import { visualizer } from 'rollup-plugin-visualizer';
import { webUpdateNotice } from '@plugin-web-update-notification/vite'

import dns from 'dns';
dns.setDefaultResultOrder('verbatim');

const isDev = process.env.NODE_ENV === 'development';
const isProd = process.env.NODE_ENV === 'production';
export default defineConfig({
  plugins: [
    progress(),
    splitVendorChunkPlugin(),
    react(),
    viteTsconfigPaths(),
    svgrPlugin(),
    webUpdateNotice({
      logVersion: true,
      hiddenDefaultNotification: true
    }),
    visualizer({
      open: false,
      gzipSize: true,
    }),
  ],
  server: {
    port: 3200,
    open: !true,
  },
  build: {
    outDir: 'build',
    sourcemap: true,
  },
  esbuild: {
    drop: isProd ? [] : ['debugger'],
    pure: isProd ? [] : ['console.log',  'console.debug', 'console.trace'],
  },
  css: {
    modules: {
      generateScopedName: isDev
        ? '[name]__[local]___[hash:base64:5]'
        : '[local]_[hash:base64:5]',
    },
  },
  resolve: {
    alias: [
      {
        find: './runtimeConfig',
        replacement: './runtimeConfig.browser',
      },
    ],
  },
});
