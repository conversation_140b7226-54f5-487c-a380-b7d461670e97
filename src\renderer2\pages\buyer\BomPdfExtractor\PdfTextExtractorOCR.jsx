import React, { useState, useRef, useEffect } from 'react';
import * as pdfjs from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.min?url'
import { useBomPdfExtractorStore } from './BomPdfExtractorStore';
import {searchProducts, getValidSearchData, useCreatePoStore, commomKeys, orderIncrementPrefix, priceUnits, getValUsingUnitKey, getFloatRemainder} from '@bryzos/giss-ui-library';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import styles from './styles/BomExtractor.module.scss';
import {ProductMapping, ProductSearch} from '@bryzos/product-search'
//import {ProductMapping, ProductSearch} from './utils/productSearch';
import textractService from './services/textractService';

import { v4 as uuidv4 } from 'uuid';
import './styles/App.css';
// Import box types configuration
import {
  BOX_TYPES,
  getBoxStyles,
  createEmptyDataArrays,
} from './config/boxTypes';
// Import TextractRBush
import TextractRBush from './textract-rbush';
import PdfjsSpatialIndex from './pdfjsSpatialIndex';
import PdfPage from './components/PdfPage';
import { routes } from 'src/renderer2/common';
import { navigatePage } from 'src/renderer2/helper';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { useNavigate } from 'react-router-dom';

const Constants = {
  PENDING: "PENDING",
  APPROVED: "APPROVED"
}

// Setup worker using CDN
pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker
// pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`

function logBox(...args) {
  //console.log(...args);
}
function logRender(...args) {
  //console.log(...args);
}

function logOcr(...args) {
  //console.log(...args);
}

function logError(...args) {
 // console.error(...args);
}

function logPdf(...args) {
 // console.log(...args);
}

// Props:
// - pdfFile: The PDF file to display
// - onError: Function to handle errors
// - onBoxesChange: Function to notify parent of boxes state
// - onExtractedDataChange: Function to notify parent of extracted data state
// - currentBoxType: The current box type selected in the parent
const PdfTextExtractorOCR = React.forwardRef((props, ref) => {
  const {
    onError,
    onBoxesChange,
    onExtractedDataChange,
    onVisibilityChange, // Optional callback for visibility changes
  } = props;
  const [numPages, setNumPages] = useState(0);
  const [fileName, setFileName] = useState('');
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfPages, setPdfPages] = useState([]);
  const [scale, setScale] = useState(3); // This is the internal scale for high-res rendering
  const [boxes, setBoxes] = useState([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState(null); // 'tl', 'tr', 'bl', 'br', 'left', 'right', 'top', 'bottom'
  const [resizingBoxId, setResizingBoxId] = useState(null);
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 });
  const [currentRect, setCurrentRect] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [hoveredBox, setHoveredBox] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const canvasRef = useRef(null);
  
  const hiResCanvasRef = useRef(null); // Hidden high-resolution canvas
  const [magnifyingGlassPosition, setMagnifyingGlassPosition] = useState({ x: 0, y: 0 });

  // Viewport detection refs and state
  const scrollableContainerRef = useRef(null);
  const containerRef = useRef(null);
  const intersectionObserverRef = useRef(null);
  const [visiblePages, setVisiblePages] = useState(new Set());
  const [fullyVisiblePages, setFullyVisiblePages] = useState(new Set());

  // Use the box styles from our configuration
  const boxStyles = getBoxStyles();

  const [extractedData, setExtractedData] = useState({});
  const [allExtractedData, setAllExtractedData] = useState({});
  //const [allBoxes, setAllBoxes] = useState({});
  const [processedBoxIds, setProcessedBoxIds] = useState(new Set());
  const [processingBoxIds, setProcessingBoxIds] = useState(new Set());
  // Note: Using pageRotations instead of a single rotation state
  //const [pageRotations, setPageRotations] = useState({});

  const [rotation, setRotation] = useState(0);

  // Store skew adjustments for each page (-45 to +45 degrees)
  const [fineRotations, setFineRotations] = useState({});
  // Store raw box data with position information for all pages
  const [rawBoxData, setRawBoxData] = useState({});
  // Use a ref to track the latest raw box data for use in setTimeout callbacks
  const rawBoxDataRef = useRef({});

  // State for TextractRBush spatial index
  //const [textractRBush, setTextractRBush] = useState(null);
  const [pdfJSPage, setPdfJSPage] = useState(null);
  const pdfPageRefs = useRef([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const { extractText, setExtractText, gridOpacity, autoDetectColumns, currentBoxType, setCurrentBoxType,
    overlapPercent, snapToGrid, pdfFile, showMagnifyingGlass, setShowMagnifyingGlass,
  doResetAll, doUndoBox, doExportCSV, setDoResetAll, setDoUndoBox, setDoExportCSV, setPdfjs, setAllBoxes, allBoxes, setSourceCanvasRefs,
  doClearAll, setDoClearAll, zoomPercentage, s3Url, bomUploadID,doNext, setDoNext, setBomData, setHasBoxes,
  isImageBasedPdf, setGeometryData,geometryData, domesticOnly, pageRotations, textractRBushInitialized, setTextractRBushInitialized,
  setScrollViewPort, updatedScrollViewPort, doResetBomPdfExtractorStore, setShowBackToBomUploadButton, textractRBush, setTextractRBush, 
  doAutoScroll, setdoAutoScroll, customBoxTypes, setPdfUrl, setPdfFileName} = useBomPdfExtractorStore();

  // const [hasInitialBoxes, setHasInitialBoxes] = useState(false);  
  // const localHasInitialBoxes = false;

  const {showCommonDialog, resetDialogStore} = useDialogStore();

  const {setIsCreatePOModule, uploadBomInitialData} = useCreatePoStore();

  const {productData} = useGlobalStore();
  const navigate = useNavigate();


  // Keep the ref in sync with the state
  useEffect(() => {
    rawBoxDataRef.current = rawBoxData;
  }, [rawBoxData]);


  // Add a state variable to track PDF rendering
  const [pdfRenderKey, setPdfRenderKey] = useState(0);

  useEffect(()=>{

    if(pdfFile){
      initializePdf();
    }
  },[pdfFile] );

  useEffect(()=>{
    setShowBackToBomUploadButton(false);
    localIsProcessing = false;
    setIsProcessing(false);
    
  },[]);


  const onPdfPageMounted = (index) => {
    if(allBoxes[index])
      pdfPageRefs.current[index].setPageBoxes(allBoxes[index]);
  }

  // useEffect(()=>{
  //   console.log("pdfpagerefs changed");
  //   if(pdfPageRefs.current.length > 0){
  //     pdfPageRefs.current.forEach((pageRef, index) => {
  //       if (pageRef && pageRef.current) {
  //         pageRef.current.setPageBoxes(allBoxes[index]);
  //       }
  //     });
  //   }
  // },[pdfPageRefs.current]);

  // Viewport detection setup
  useEffect(() => {
    const setupIntersectionObserver = () => {
      // Clean up existing observer
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }

      // Create new observer with options
      const observerOptions = {
        root: scrollableContainerRef.current,
        rootMargin: '50px', // Start observing 50px before entering viewport
        threshold: [0, 0.1, 0.5, 0.9, 1.0] // Multiple thresholds for granular detection
      };

      const handleIntersection = (entries) => {
        entries.forEach((entry) => {
          const pageIndex = parseInt(entry.target.dataset.pageIndex);

          if (entry.isIntersecting) {
            // Page is at least partially visible
            setVisiblePages(prev => new Set([...prev, pageIndex]));

            // Check if page is fully visible (90% threshold)
            if (entry.intersectionRatio >= 0.9) {
              setFullyVisiblePages(prev => new Set([...prev, pageIndex]));
            } else {
              setFullyVisiblePages(prev => {
                const newSet = new Set(prev);
                newSet.delete(pageIndex);
                return newSet;
              });
            }
          } else {
            // Page is not visible
            setVisiblePages(prev => {
              const newSet = new Set(prev);
              newSet.delete(pageIndex);
              return newSet;
            });
            setFullyVisiblePages(prev => {
              const newSet = new Set(prev);
              newSet.delete(pageIndex);
              return newSet;
            });
          }
        });
      };

      intersectionObserverRef.current = new IntersectionObserver(handleIntersection, observerOptions);
    };

    // Setup observer when PDF pages are available
    if (pdfPages.length > 0) {
      setupIntersectionObserver();
    }

    // Cleanup on unmount
    return () => {
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }
    };
  }, [pdfPages.length]); // Re-setup when number of pages changes

  // Observe page containers when they're rendered
  useEffect(() => {
    if (intersectionObserverRef.current && pdfPages.length > 0) {
      // Observe all page containers
      const pageContainers = scrollableContainerRef.current?.querySelectorAll('[data-page-index]');
      pageContainers?.forEach((container) => {
        intersectionObserverRef.current.observe(container);
      });

      // Cleanup function to unobserve when pages change
      return () => {
        if (intersectionObserverRef.current) {
          pageContainers?.forEach((container) => {
            intersectionObserverRef.current.unobserve(container);
          });
        }
      };
    }
  }, [pdfPages, intersectionObserverRef.current]); // Re-observe when pages or observer changes

  // Debug effect to log visibility changes and notify parent
  useEffect(() => {
    const visiblePagesArray = Array.from(visiblePages).sort((a, b) => a - b);
    const fullyVisiblePagesArray = Array.from(fullyVisiblePages).sort((a, b) => a - b);

    // Notify parent component if callback is provided
    if (onVisibilityChange) {
      const visibilityInfo = {
        visiblePages: visiblePagesArray,
        fullyVisiblePages: fullyVisiblePagesArray,
        totalPages: pdfPages.length,
        visibleCount: visiblePages.size,
        fullyVisibleCount: fullyVisiblePages.size
      };
      onVisibilityChange(visibilityInfo);
    }
  }, [visiblePages, fullyVisiblePages, pdfPages.length, onVisibilityChange]);

  const initializePdf = async () => {
    if (!pdfFile) return;

    try {
      setFileName(pdfFile.name);
      const arrayBuffer = await pdfFile.arrayBuffer();
      const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
      if(!isImageBasedPdf ){
        const pdfjsSpatialIndex = new PdfjsSpatialIndex();
        await pdfjsSpatialIndex.initialize(pdf);
        setTextractRBush(pdfjsSpatialIndex);
      }

      setPdfjs(pdf);
      setNumPages(pdf.numPages);
      const pages = [];
      const tempArr = [];
      for(let i = 0; i < pdf.numPages; i++){
        const page = await pdf.getPage(i+1);
        pages.push(page);
        tempArr.push([]);
        //setPdfPages(prev=>([...prev, page]));
      }
      //setAllBoxes(tempArr);
      setPdfPages(pages);
      setSourceCanvasRefs(new Array(pdf.numPages).fill(null));
    } catch (error) {
      console.error('Failed to initialize PDF', error);
    }
  };

  const [displayArrays, setDisplayArrays] = useState(null);

  useEffect(() => {
    if (extractText) {
      extractImages();
    }
  }, [extractText]);

  useEffect(() => {
    if (doResetAll) {
      if(pdfPageRefs.current.length === 0) return;
      pdfPageRefs.current.forEach(ref => ref?.clearBoxes());
      setDoResetAll(false);
    }
    //This is a hack. After all the pages boxes have been cleared reset allBoxes. 
    setTimeout(()=>{
      const all_boxes = pdfPageRefs.current.map(ref => ref?.getBoxes() || []);
      setAllBoxes(all_boxes);
      setHasBoxes(false);
    },300)
  }, [doResetAll]);

  useEffect(() => {
    if (doClearAll) {
      if(visiblePages.size>0){
        visiblePages.forEach(pageIndex => {
          pdfPageRefs.current[pageIndex].clearBoxes();
        });
      }
      setDoClearAll(false);
    }
  }, [doClearAll]);

  useEffect(() => {
    if (doUndoBox) {
      const undoBoxesStack = [];
      allBoxes.forEach((pageBoxes, index)=>{
        if(pageBoxes)
          pageBoxes.forEach(box=>{
            undoBoxesStack.push({index:index, box:box});
          })
      });
      undoBoxesStack.sort((a, b) => b.box.timeStamp - a.box.timeStamp);
      if(undoBoxesStack.length > 0){
        pdfPageRefs.current[undoBoxesStack[0].index].undoBox(undoBoxesStack[0].box);
      }
      setDoUndoBox(false);
    }
  }, [doUndoBox]);

  useEffect(() => {
    if (doExportCSV) {
      setDoExportCSV(false);
    }
  }, [doExportCSV]);

  useEffect(() => {
    if (doNext) {
      extractImages();
      setDoNext(false);
    }
  }, [textractRBush]);

  useEffect(() => {
    if (doNext && textractRBush) {
      extractImages();
      setDoNext(false);
    }
  }, [doNext]);

  // Extract images from boxes
  const extractImages = async () => {
    setExtractText(false);

    logOcr('Starting OCR extraction process', {
      pdfFilename: pdfFile?.name,
      pageNumber,
      totalBoxes: boxes.length,
      processedBoxes: processedBoxIds.size,
      scale,
      rotation: pageRotations[`page${pageNumber}`] || 0,
      fineRotation: fineRotations[`page${pageNumber}`] || 0,
    });
    const startTime = performance.now();


    if (!pdfFile ||  !pdfPageRefs.current.length === 0) {
      const reason = !pdfFile
        ? 'No PDF loaded'
        :  'Canvas not available';
      logError(`OCR extraction failed: ${reason}`, {
        pdfFilename: pdfFile?.name,
        boxCount: boxes.length,
        canvasAvailable: !!canvasRef.current,
      });
      return;
    }

    // Check if TextractRBush is initialized
    
    if (textractRBush) {
      //processBoxes(boxes);
      const currentPageKey = `page${pageNumber}`;
      const tempBoxes = {};
      tempBoxes[currentPageKey] = boxes;

      logBox(`Saving boxes for page ${pageNumber}`, {
        pageNumber,
        boxCount: boxes.length,
        boxTypes: boxes.reduce((acc, box) => {
          acc[box.type] = (acc[box.type] || 0) + 1;
          return acc;
        }, {}),
      });
      // let all_boxes = {...allBoxes, ...tempBoxes };
      // setAllBoxes((prev) => {
      //   return {...prev, ...tempBoxes };
      // })
      const all_boxes = pdfPageRefs.current.map(ref => ref?.getBoxes() || []);
      processAllBoxes(all_boxes);

    }
    else{
      setProcessWhenReady(true)
    }
  };
  let localIsProcessing = false;

  const [processWhenReady, setProcessWhenReady] = useState(false);

  const processAllBoxes = (all_boxes) => {
    if(localIsProcessing||isProcessing) return;
    localIsProcessing = true;
    setIsProcessing(true);
    setGeometryData(all_boxes)
    //for(let pageID in all_boxes){
    all_boxes.forEach((boxes, index)=>{
      const pageNumber = index+1//pageID.slice(4);
      const pageID = `page${pageNumber}`;

      // Get canvas dimensions for this page
      const canvasSize = pdfPageRefs.current[pageNumber-1]?.getCanvasSize() || { width: 0, height: 0 };

      // Convert normalized boxes to canvas coordinates
      // const canvasBoxes = boxes.map(box => ({
      //   ...box,
      //   rect: {
      //     x: box.rect.x * canvasSize.width,
      //     y: box.rect.y * canvasSize.height,
      //     width: box.rect.width * canvasSize.width,
      //     height: box.rect.height * canvasSize.height
      //   }
      // }));


      //const pageData = processBoxes(canvasBoxes, Number(pageNumber));
      const pageData = processBoxes(boxes, Number(pageNumber));
      if(Object.keys(pageData.displayArrays)?.length>0){
        setDisplayArrays(prev=>({...prev, [pageID]: {
          pageNumber,
          rowCount:pageData.displayArrays[Object.keys(pageData.displayArrays)[0]].length,
          texts:pageData.displayArrays}}));
        //setRawText(prev=>({...prev, [pageNumber]: pageData.rowText}));
      }else{
        localIsProcessing = false;
        setIsProcessing(false);
      }
    })
  }
  function objectOfArraysToArrayOfObjects(obj) {
    const keys = Object.keys(obj);
    const length = obj[keys[0]].length;
    return Array.from({ length }).map((_, i) =>
      keys.reduce((acc, key) => {
        acc[key] = obj[key][i];
        return acc;
      }, {})
    );
  }

  const getQtyUnit = (qtyUnit) => {
    switch(qtyUnit){
      case 'ft':
        return 'Ft';
      case 'lb':
        return 'Lb';
      case 'ea':
        return 'PC';
      case 'pc':
        return 'PC';
      case 'net tons':
        return 'Net Ton';
      case 'cwt':
        return 'CWT';
      default:
        return 'PC';
    }
  }

  useEffect(()=>{
    if(!geometryData || !bomUploadID) return;
    const geometryPayload = {
        "bom_upload_id": bomUploadID,
        "selection_data":  {geometryData: geometryData, customBoxTypes:customBoxTypes}
    }
    textractService.saveGeometryData(geometryPayload);
  },[geometryData]);

  useEffect(()=>{
    if(!displayArrays) return;
    const extractedData = [];
    let weightMapping = {};
    const confidanceRange = [
      {
        min_match_count: 2,
        max_match_count: 19,
        confidence:50
      },
      {
        min_match_count: 20,
        max_match_count: null,
        confidence:0
      }
    ];

    const filteredProductData = productData.filter((product)=>!product.is_safe_product_code)

    filteredProductData.forEach((product)=>{
      weightMapping[product.id] = Number(product[`${orderIncrementPrefix}${priceUnits.pc}`])/Number(product[`${orderIncrementPrefix}${priceUnits.lb}`]);
    })

    Object.keys(displayArrays).forEach((pageID)=>{
      objectOfArraysToArrayOfObjects(displayArrays[pageID].texts).forEach(obj=>{
        extractedData.push({...obj, pageNumber: displayArrays[pageID].pageNumber});
      });
    })

    const result = getProducts(filteredProductData, extractedData, weightMapping, confidanceRange);

  },[displayArrays]);

  function parseLength(desc) {
    const lenPart = desc.split(/x/i).pop().trim();
    const token = lenPart.split(/\s+/)[0];
    const cleaned = token.replace(/ft|in|'|"/gi, '');
    const [feetStr = '', inchStr = ''] = cleaned.split('-');
    const feet = parseInt(feetStr, 10) || 0;
    let inches = 0;
    if (inchStr) {
      if (inchStr.includes('/')) {
        const [num, den] = inchStr.split('/').map(Number);
        inches = num/den;
      } else {
        inches = parseInt(inchStr, 10) || 0;
      }
    }
    return { feet, inches, totalFeet: feet + inches/12 };
  }

  const getSplitString = (splitObj)=>{
    console.log(splitObj)
    return [
      splitObj?.shape || '',
      splitObj?.category || '',
      splitObj?.dimensions || '',
      splitObj?.spec || '',
      splitObj?.grade || '',
      splitObj?.length || ''
    ].filter(Boolean).join(' ');
  }

  const doSearch = async(extractedData, productData, weightMapping,confidanceRange)=>{
    const extractedSearchString = [
      extractedData.description || '',
      extractedData.specification || '',
      extractedData.grade || '',
      extractedData.length || ''
    ].filter(Boolean).join(' ');
    const productMapping = new ProductMapping();
    const productSearch = new ProductSearch();
    let searchResult;
    let splitStr;
    let finalResult;
    let result;
    if(extractedData.description && !extractedData.length){
      const parsedLength = parseLength(extractedData.description);
      if(parsedLength && parsedLength.totalFeet){
        searchResult = await productSearch.search({...extractedData, length: parsedLength.totalFeet });
        console.log('searchResult', searchResult);
        if(searchResult.length){
          const [value, unit] = searchResult.length.split(' ');
          if(unit && unit.toLowerCase() === 'ft'){
            searchResult.length = Number(value)*12;
          }else{
            searchResult.length = Number(value);
          }
          splitStr = getSplitString(searchResult)
          result = await productMapping.searchProductMatches(productData, searchResult, null, 1, weightMapping, 120,confidanceRange);
          result.splitString = splitStr;
          result.extractedString = extractedSearchString
          
          if(result.confidence === 100){
            return result;
          }
        }
      }

    }
    if(extractedData.description && extractedData.description.trim() !== ''){
      searchResult = await productSearch.search(extractedData);
      splitStr = getSplitString(searchResult)
      result = await productMapping.searchProductMatches(productData, searchResult, null, 1, weightMapping, 120,confidanceRange);
      result.splitString = splitStr;
      result.extractedString = extractedSearchString
    }else{
      splitStr = '';
      result = await productMapping.searchProductMatches(productData, extractedData, null, 1, weightMapping, 120,confidanceRange);
      result.splitString = splitStr;
      result.extractedString = extractedSearchString
    }

    if(result.confidence !== 100 && extractedData.length){
      let length;
      if(extractedData.length.includes('-')){
        length = extractedData.length.replace(/\s+/g, "");
      }else{
        let lengthArr = extractedData.length.split(" ");
          lengthArr = lengthArr.filter(l => l.trim() !== "");
          if(lengthArr.length === 2){
            if(lengthArr[0].endsWith("1")){
              lengthArr[0] =  lengthArr[0].slice(0, -1) + "'";
            }
            if(lengthArr[1].endsWith("11")){
              lengthArr[1] =  lengthArr[1].slice(0, -2) + '"';
            }
          }
          length = lengthArr.join("-");
      }
      if(extractedData.description && extractedData.description.trim() !== ''){
        searchResult = await productSearch.search({...extractedData, length});
        splitStr = getSplitString(searchResult)
        finalResult = await productMapping.searchProductMatches(productData, searchResult, null, 1, weightMapping, 120,confidanceRange);
        finalResult.splitString = splitStr;
        finalResult.extractedString = extractedSearchString;
      }else{
        splitStr = '';
        finalResult = await productMapping.searchProductMatches(productData, extractedData, null, 1, weightMapping, 120,confidanceRange);
        finalResult.splitString = splitStr;
        finalResult.extractedString = extractedSearchString;
      }
      
      if(finalResult.confidence === 100){
        return finalResult;
      }else{
        return result
      }
    }else{
      return result;
    }
  }

  const getProducts = async (productData, extractedData, weightMapping, confidanceRange) => {
    try{
    const result = [];
    let finalResult;
    console.log(extractedData);
    for(let index = 0; index < extractedData.length; index++){
      console.log(extractedData[index].quantity);
      if((!extractedData[index]?.description || (extractedData[index]?.description+'')?.trim() === "") &&
        (!extractedData[index]?.length || (extractedData[index]?.length+'')?.trim() === "") &&
        (!extractedData[index]?.grade || (extractedData[index]?.grade+'')?.trim() === "")&&
        (!extractedData[index]?.quantity || (extractedData[index]?.quantity+'')?.trim() === "") &&
        (!extractedData[index]?.specification || (extractedData[index]?.specification+'')?.trim() === "") &&
        (!extractedData[index]?.partNumber || (extractedData[index]?.partNumber+'')?.trim() === "") &&
        (!extractedData[index]?.weight || (extractedData[index]?.weight+'')?.trim() === "")){
        continue;
      }

      finalResult = await doSearch(extractedData[index], productData, weightMapping, confidanceRange);
      const descriptionMatches = finalResult.confidence === 100;

      const qum = (extractedData[index]?.quantityum)?getQtyUnit(extractedData[index]?.quantityum.toLowerCase()):getQtyUnit(extractedData[index].qtyUnit);
      const quantity = extractedData[index].quantity? extractedData[index].quantity + '': null;
      if(!extractedData[index].quantity && finalResult.confidence === 100){
        finalResult.confidence = 50;
      }else if(extractedData[index].quantity && finalResult.confidence === 100){
        const product  = productData.filter(item=>item.Product_ID === finalResult.productIds[0]);
        const orderIncrement = getValUsingUnitKey(product[0], qum.toLowerCase(), orderIncrementPrefix);
        if(quantity && orderIncrement){
          if (quantity > 0 && getFloatRemainder(quantity, orderIncrement) !== 0) {
            finalResult.confidence = 50;
          }
        }
      }
      const resultObj = {
        original_line_status: finalResult.confidence === 100 ? Constants.APPROVED : Constants.PENDING,
        status: finalResult.confidence === 100 ? Constants.APPROVED : Constants.PENDING,
        confidence: finalResult.confidence,
        product_tag: extractedData[index].partNumber? extractedData[index].partNumber : null,
        description: extractedData[index].description,
        specification: extractedData[index].specification,
        search_string: finalResult.searchKeywords,
        pdf_string: finalResult.extractedString,
        split_string: finalResult.splitString,
        grade: extractedData[index].grade,
        shape: null,
        qty: quantity,
        qty_unit: qum,
        price_per_unit: "0.0000",
        price_unit: null,
        length: extractedData[index].length,
        weight_per_quantity: null,
        matched_product_count: finalResult.productIds.length,
        matched_products: finalResult.productIds,
        selected_products: (descriptionMatches)?[finalResult.productIds[0]]:[],
        current_page: extractedData[index].pageNumber,
        total_pages: numPages,
        product_index: (index +1),
        domestic_material_only: domesticOnly? true : null,
        buyer_line_total: "0.0000",
        last_updated_product: 0,
        line_weight: "0.00"
      }

      result.push(resultObj);



  }
  const wrapperObj = {
    "file_name": fileName,
    "actual_file_name": fileName,
    "status": "COMPLETED",
    "s3_url": s3Url,
    "bom_upload_id": bomUploadID,
    "total_pages": numPages,
    "material_total": "0.0000",
    "sales_tax": "0.00",
    "total_weight": "0.00",
    "total_purchase": "0.00",
    "deposit": "0.00",
    "subscription": "0.00",
    "review_status": "PENDING REVIEW",
    "line1": uploadBomInitialData.shipping_details.line1,
    "line2": uploadBomInitialData.shipping_details.line2,
    "city": uploadBomInitialData.shipping_details.city,
    "state_id": uploadBomInitialData.shipping_details.state_id,
    "zip": uploadBomInitialData.shipping_details.zip,
    "bom_name": uploadBomInitialData.internal_po_number,
    "bom_type": uploadBomInitialData.order_type,
    "delivery_date": uploadBomInitialData.delivery_date,
    "result": result
  }

  console.log(wrapperObj);
  if(wrapperObj.result.length > 0){
    await textractService.saveExtractedData(wrapperObj);
    const bodData = await textractService.getBomData(bomUploadID);
    doResetBomPdfExtractorStore.value = false;
    setPdfUrl(bodData.data[0].s3_url);
    setPdfFileName(bodData.data[0].actual_file_name);
    setBomData(bodData.data[0]);
    setShowBackToBomUploadButton(true);
    setIsCreatePOModule(false);
    navigate(routes.bomUploadReview, { state: { from: 'bomPdfExtractor' } })
  }else{
    showCommonDialog(null, "No data was extracted, please try adjusting the boxes.", null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
    localIsProcessing = false;
    setIsProcessing(false);
  } } catch(error){
    console.error(error)
  }
};



  // --- CSV Export Logic ---
  const exportCSV = () => {
   const keys   = Object.keys(BOX_TYPES);                      // ['description','part_number',…]
   const labels = keys.map(id => BOX_TYPES[id].label);         // ['Description','Part Number',…]

    // 1) Build header row with "Page" first
    const headerRow = ['Page', ...labels].join(',');

    // 2) Flatten all pages & rows into CSV lines
    const csvRows = [headerRow];
    for (let page = 1; page <= numPages; page++) {
      const pageKey  = `page${page}`;
      const pageData = displayArrays[pageKey];
      const rowCount = pageData?.rowCount ?? 0;

      for (let i = 0; i < rowCount; i++) {
        // start with page number
        const row = [page];
        // then each column's text (or blank)
        keys.forEach(id => {
          const txt = pageData?.texts?.[id]?.[i] ?? '';
          // escape any internal quotes
          row.push(`"${txt.replace(/"/g,'""')}"`);
        });
        csvRows.push(row.join(','));
      }
    }

    // 3) Create a blob and trigger download
    const blob = new Blob([csvRows.join('\n')], { type: 'text/csv;charset=utf-8;' });
    const url  = URL.createObjectURL(blob);
    const a    = document.createElement('a');
    a.href     = url;
    a.download = 'table_export.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const drawAllPAges = () => {
    const headers = Object.keys(BOX_TYPES); // preserves insertion order
    return (
      <table className={styles.table} border="1" cellPadding="4" cellSpacing="0">
        <thead>
          <tr>
            <th>No.</th>
            {headers.map((id) => (
              <th key={id}>{BOX_TYPES[id].label}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {Array.from({ length: numPages }, (_, idx) => {
            const pageNumber = idx + 1;
            const pageKey = `page${pageNumber}`;
            const pageData = displayArrays[pageKey];
            const rowCount = pageData ? pageData.rowCount : 1;

            return (
              <React.Fragment key={pageKey}>
                <tr>
                  <td colSpan={headers.length}>
                    Page {pageNumber}
                  </td>
                </tr>

                {Array.from({ length: rowCount }, (_, rowIdx) => (
                  <tr key={`${pageKey}-row-${rowIdx}`}>
                    <td>{rowIdx + 1}</td>
                    {headers.map((id) => {
                      const value = pageData?.texts?.[id]?.[rowIdx] ?? '';
                      return <td key={id}>{value}</td>;
                    })}
                  </tr>
                ))}
              </React.Fragment>
            );
          })}
        </tbody>
      </table>
    );
  };

  
  function parseQuantity(input) {
    const pattern = /^\s*(\d{1,3}(?:,\d{3})*|\d+)(?:\s*(ft|lb|ea|pc|net tons|cwt))?(?:\s+.*)?$/i;
    const match = input.match(pattern);
    if (!match) return null;
    const rawNumber = match[1];
    let unit = match[2] || null;
    if (unit) unit = unit.toLowerCase();
    const numberValue = Number(rawNumber.replace(/,/g, ''));
    return { numberValue, unit };
  }

  function parseDate(input){

    return input;
  }

  function parseNumber(input){

    return input;
  }

  function parseAlphaNumeric(input){

    return input;
  }



  const processBoxes = (boxes_to_process, pageNumber) => {
      const rawTextForProcessing = [];

      // Process each box
      for (const box of boxes_to_process) {
        const { id, rect, type } = box;
        // Mark this box as being processed
        //setProcessingBoxIds((prev) => new Set([...prev, id]));

          // Convert box coordinates to normalized coordinates (0-1 range)

          const canvasSize = pdfPageRefs.current[pageNumber-1]?.getCanvasSize() || { width: 0, height: 0 };
          const canvasWidth = canvasSize.width;
          const canvasHeight = canvasSize .height;
          const rotation = pageRotations;
          const zoomFactor = zoomPercentage / 100;

          // Coordinates are already in canvas space from processAllBoxes conversion
          const rectInCanvas = {
            top: rect.y * canvasHeight,
            left: rect.x * canvasWidth,
            width: rect.width * canvasWidth,
            height: rect.height * canvasHeight
          };

          // Transform based on rotation
          const normalizedRect = transformBoundingBox(
            {
              top: rect.y,
              left: rect.x,
              width: rect.width,
              height: rect.height
            },
            rotation,
            1,
            1
          );

          // Normalize to 0-1 range for query
          // const normalizedRect = {
          //   left: transformedRect.left / canvasWidth,
          //   top: transformedRect.top / canvasHeight,
          //   width: transformedRect.width / canvasWidth,
          //   height: transformedRect.height / canvasHeight
          // };

          // Query the TextractRBush spatial index
          const overlapRatio = overlapPercent/100;
          const pageSize = { width: canvasWidth, height: canvasHeight };
          // Explicitly pass the rotation parameter and zoomFactor to ensure consistent coordinate transformations

          const results = textractRBush.query(normalizedRect, .2, pageSize, pageNumber, rotation, zoomFactor);
          if(type === 'quantity'){
            results.forEach((item) => {
              if(item?.text?.trim() === '') return;
              const parsedQuantity = parseQuantity(item.text);
              if (parsedQuantity) {
                item.text = parsedQuantity.numberValue;
                item.unit = parsedQuantity.unit;
              }
            });
          }else if(BOX_TYPES[type]?.regex ){
            results.forEach((item) => {
              if(item?.text?.trim() === '') return;
              
              BOX_TYPES[type].regex.forEach((regexObj) => {
                switch(regexObj.type){
                  case 'replace':
                    item.text = item.text.replace(regexObj.regex, regexObj.substiture);
                    break;
                  case 'remove':
                    item.text = item.text.replace(regexObj.regex, '');
                    break;
                  case 'extract':
                    item.text = item.text.match(regexObj.regex)[0];
                    break;
                }
              });
            });
          }

          const averageX = results.reduce((sum, item) => sum + item.boundingBox.x, 0) / results.length;
          rawTextForProcessing.push({ id, type, textRows: results, averageX, rect: box.rect, pageSize });
      }
      let masterArr = [];


      //check for multiple boxes of same type
      const boxInfoForType = {};
      let hasMultipleBoxes = false;
      let arrTemp = [];
      const hieghestX = {};
      for (let key in BOX_TYPES) {
        arrTemp = rawTextForProcessing.filter((item)=>{
          if(key === item.type){
            hieghestX[key] = Math.max(hieghestX[key] || 0, item.averageX);
            return true;
          }
          return false;
        });
        if(arrTemp.length > 1){
          hasMultipleBoxes = hasMultipleBoxes|| checkForParallelBoxes(arrTemp);
        }
        boxInfoForType[key] = {boxCount:arrTemp.length, hieghestX: hieghestX[key] };
      }



      const processedArrays = {};
      //This marks the x position after which a new map starts hence a new row in master array
      let delimiterX = Infinity;
      const masterArrForMultipleColumns = [[],[]];

      if(hasMultipleBoxes){

        for(let key in boxInfoForType){
          if(boxInfoForType[key].boxCount > 1){
            delimiterX = Math.min(boxInfoForType[key].hieghestX, delimiterX);
          }
        }

        //create a master array using with x and y
        rawTextForProcessing.forEach((boxData) => {
          boxData.textRows.forEach((row) => {
            //Assumption ass we are extracting tabular data all texts in a box would have a very similar x hence we can take the boxes avg x
            if(row.boundingBox.x+10 < delimiterX){
              masterArrForMultipleColumns[0].push(row.boundingBox.y);
            }else{
              masterArrForMultipleColumns[1].push(row.boundingBox.y);
            }
          });
        })
        //Sort on y
        const zoomFactor = zoomPercentage / 100;
        masterArrForMultipleColumns[0].sort((a, b) => a - b);
        masterArrForMultipleColumns[0] = groupAndAverage(masterArrForMultipleColumns[0], 2*zoomFactor)
        masterArrForMultipleColumns[1].sort((a, b) => a - b);
        masterArrForMultipleColumns[1] = groupAndAverage(masterArrForMultipleColumns[1], 2*zoomFactor)

        rawTextForProcessing.forEach((boxData)=>{
          if(!processedArrays[boxData.type]) {
            processedArrays[boxData.type] = new Array(masterArrForMultipleColumns[0].length + masterArrForMultipleColumns[1].length).fill('');
          };
          
          if(boxData.type === 'quantity'){
            processedArrays['qtyUnit'] = new Array(masterArrForMultipleColumns[0].length + masterArrForMultipleColumns[1].length).fill(null);
          }
          if(boxData.averageX + 10 < delimiterX){
            boxData.textRows.forEach((row) => {
              const closestIndex = getClosestIndex(masterArrForMultipleColumns[0], row.boundingBox.y);
              processedArrays[boxData.type][closestIndex] = row.text;
              if(boxData.type === 'quantity' && row.unit){
                processedArrays['qtyUnit'][closestIndex] = row.unit;
              }
            });
          }else{
            boxData.textRows.forEach((row) => {
              const closestIndex = getClosestIndex(masterArrForMultipleColumns[1], row.boundingBox.y);
              processedArrays[boxData.type][masterArrForMultipleColumns[0].length + closestIndex] = row.text;
              if(boxData.type === 'quantity' && row.unit){
                processedArrays['qtyUnit'][masterArrForMultipleColumns[0].length + closestIndex] = row.unit;
              }
            });
          }

        });

      }else{
        rawTextForProcessing.forEach((boxData) => {
          boxData.textRows.forEach((row) => {
            masterArr.push(row.boundingBox.y);
          });
        })
        const zoomFactor = zoomPercentage / 100;
        masterArr.sort((a, b) => a - b);
        masterArr = groupAndAverage(masterArr, 3*zoomFactor)

        rawTextForProcessing.forEach(
          (boxData) => {
            if(!processedArrays[boxData.type]) {
              processedArrays[boxData.type] = new Array(masterArr.length).fill('');
            };
            if(boxData.type === 'quantity'){
              processedArrays['qtyUnit'] = new Array(masterArr.length).fill(null);
            }

            boxData.textRows.forEach((row) => {
              const closestIndex = getClosestIndex(masterArr, row.boundingBox.y);
              processedArrays[boxData.type][closestIndex] = row.text;
              if(boxData.type === 'quantity' && row.unit){
                processedArrays['qtyUnit'][closestIndex] = row.unit;
              }
            });

          }
        )
      }

      //setDisplayArrays( processedArrays);

      //setRawText(rawTextForProcessing);
      return {
        displayArrays: processedArrays,
        rowText: rawTextForProcessing
      }
  }

  function checkForParallelBoxes(arrRects) {
    const tolerance = 3 *zoomPercentage/100;
    let retValue = false;
    arrRects.forEach(({rect, pageSize}, i)=>{
      const from = rect.y*pageSize.height - tolerance;
      const to = rect.y*pageSize.height + rect.height*pageSize.height - tolerance;

      for(let index = i+1; index < arrRects.length; index++){
        const rect = {x: arrRects[index].rect.x*arrRects[index].pageSize.width, 
                      y: arrRects[index].rect.y*arrRects[index].pageSize.height, 
                      width: arrRects[index].rect.width*arrRects[index].pageSize.width, 
                      height: arrRects[index].rect.height*arrRects[index].pageSize.height};
        
        if( rect.y >= from && rect.y <= to){
          retValue = true;
          break;
        }
      }
    })
    return retValue;
  }


  function transformBoundingBox({ top, left, width, height }, rotation, canvasWidth, canvasHeight) {
    const rot = ((rotation % 360) + 360) % 360;
    let newLeft, newTop;
    switch (rot) {
      case 0:
        newLeft = left;
        newTop  = top;
        break;
      case 90:
        newLeft = canvasHeight - top - height;
        newTop  = left;
        break;
      case 180:
        newLeft = canvasWidth  - left  - width;
        newTop  = canvasHeight - top   - height;
        break;
      case 270:
        newLeft = top;
        newTop  = canvasWidth  - left  - width;
        break;
      default:
        throw new Error('Rotation must be a multiple of 90°');
    }
    return { left: newLeft, top: newTop, width, height };
  }


  function getClosestIndex(arr, target) {
    let left = 0;
    let right = arr.length - 1;
    while (left < right) {
        const mid = Math.floor((left + right) / 2);
        if (arr[mid] === target) return mid;
        if (arr[mid] < target) left = mid + 1;
        else right = mid;
    }
    // left is now the insertion point
    if (left === 0) return 0;
    // compare which neighbor is closer
    return (Math.abs(arr[left] - target) < Math.abs(arr[left - 1] - target))
        ? left
        : left - 1;
}

  function groupAndAverage(arr, tolerance) {
    const result = [];
    if (arr.length === 0) return result;
    let group = [arr[0]];
    for (let i = 1; i < arr.length; i++) {
      if (arr[i] - arr[i - 1] <= tolerance) {
        group.push(arr[i]);
      } else {
        const sum = group.reduce((a, b) => a + b, 0);
        result.push(sum / group.length);
        group = [arr[i]];
      }
    }
    const sum = group.reduce((a, b) => a + b, 0);
    result.push(sum / group.length);
    return result;
  }


  function groupAndAverageWithDelimiter(arr, delimiterX, toleranceX = 10, tolerance = 2) {
    const result = [];
    if (arr.length === 0) return result;
    let group = [arr[0]];
    for (let i = 1; i < arr.length; i++) {
      if (arr[i] - arr[i - 1] <= tolerance) {
        group.push(arr[i]);
      } else {
        const sum = group.reduce((a, b) => a + b, 0);
        result.push(sum / group.length);
        group = [arr[i]];
      }
    }
    const sum = group.reduce((a, b) => a + b, 0);
    result.push(sum / group.length);
    return result;
  }

  function detectRows(rect) {
    // Try to use high-resolution canvas for better text detection
    const useHiResCanvas = hiResCanvasRef.current !== null;
    const canvas = useHiResCanvas ? hiResCanvasRef.current : canvasRef.current;

    if (!canvas) return [];

    const ctx = canvas.getContext('2d', { willReadFrequently: true });
    if (!ctx) return [];

    // Use appropriate scale based on which canvas we're using
    const currentScale = useHiResCanvas ? scale * 3 : scale;

    const sx = Math.floor(rect.x * currentScale);
    const sy = Math.floor(rect.y * currentScale);
    const sw = Math.floor(rect.width * currentScale);
    const sh = Math.floor(rect.height * currentScale);

    const imageData = ctx.getImageData(sx, sy, sw, sh);
    const data = imageData.data;

    const width = imageData.width;
    const height = imageData.height;

    // Log which canvas we're using for row detection
    logOcr(`Detecting rows using ${useHiResCanvas ? 'high-resolution' : 'standard'} canvas`, {
      canvasType: useHiResCanvas ? 'high-resolution' : 'standard',
      scale: currentScale,
      dimensions: { width, height },
      boxRect: { x: sx, y: sy, width: sw, height: sh }
    });

    const rowVariations = new Array(height).fill(0);

    // Step 1: Calculate variation per row
    for (let y = 0; y < height; y++) {
      let lastGray = -1;
      let totalVariation = 0;

      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4;
        const r = data[idx];
        const g = data[idx + 1];
        const b = data[idx + 2];
        const gray = 0.299 * r + 0.587 * g + 0.114 * b;

        if (lastGray >= 0) {
          totalVariation += Math.abs(gray - lastGray);
        }
        lastGray = gray;
      }

      rowVariations[y] = totalVariation / (width - 1);
    }

    // Step 2: Smooth the variations a little (simple moving average)
    const smoothedVariations = rowVariations.map((_, idx) => {
      let sum = 0,
        count = 0;
      for (let offset = -1; offset <= 1; offset++) {
        const neighborIdx = idx + offset;
        if (neighborIdx >= 0 && neighborIdx < height) {
          sum += rowVariations[neighborIdx];
          count++;
        }
      }
      return sum / count;
    });

    // Step 3: Calculate dynamic threshold
    const avgVariation =
      smoothedVariations.reduce((sum, v) => sum + v, 0) / height;
    const variationThreshold = avgVariation * 0.5;

    // Step 4: Find row midpoints
    const rowPositions = [];
    let inTextBlock = false;
    let blockStart = 0;

    for (let y = 1; y < height - 1; y++) {
      const isText = smoothedVariations[y] > variationThreshold;

      if (!inTextBlock && isText) {
        inTextBlock = true;
        blockStart = y;
      }

      if (inTextBlock && !isText) {
        if (y - blockStart > 5) {
          const midY = Math.floor(blockStart + (y - blockStart) / 2);

          // Use native coordinates without conversion
          const rowY = sy + midY;
          const rowX = sx + Math.floor(width / 2);

          // Include both X and Y coordinates in their native coordinate system
          rowPositions.push({
            y: rowY,  // Native Y position (high-res or standard)
            x: rowX   // Native X position (high-res or standard)
          });
        }
        inTextBlock = false;
      }
    }

    // Last row edge case
    if (inTextBlock && height - blockStart > 5) {
      const midY = Math.floor(blockStart + (height - blockStart) / 2);

      // Use native coordinates without conversion
      const rowY = sy + midY;
      const rowX = sx + Math.floor(width / 2);

      rowPositions.push({
        y: rowY,
        x: rowX
      });
    }

    // If no rows detected but significant content, fallback
    if (rowPositions.length === 0 && avgVariation > 5) {
      // Use native coordinates without conversion
      const rowY = sy + Math.floor(height / 2);
      const rowX = sx + Math.floor(width / 2);

      rowPositions.push({
        y: rowY,
        x: rowX
      });
    }

    // Log the detected row positions
    logOcr(`Detected ${rowPositions.length} text rows using native coordinates`, {
      rowCount: rowPositions.length,
      usingHiRes: useHiResCanvas,
      coordinateSystem: useHiResCanvas ? 'high-resolution' : 'standard',
      firstRowY: rowPositions.length > 0 ? rowPositions[0].y : null,
      lastRowY: rowPositions.length > 0 ? rowPositions[rowPositions.length - 1].y : null,
      avgVariation
    });

    return rowPositions;
  }

  function getFromId(id, array) {
    return array.find((item) => item.id === id);
  }

  function parseOpenAIResponse(response) {
    if (typeof response !== 'string') {
      console.error('Invalid input: Response must be a string');
      return null;
    }

    try {
      if (response.startsWith('```json')) {
        response = response
          .replace(/^```json/, '')
          .replace(/```$/, '')
          .trim();
      } else if (response.startsWith('```')) {
        response = response.replace(/^```/, '').replace(/```$/, '').trim();
      }

      const parsed = JSON.parse(response);
      return parsed;
    } catch (error) {
      console.error('Failed to parse response:', error.message);
      return null;
    }
  }

  // Note: Unused functions have been removed to clean up the code

  // Note: Unused mergeExtractedData function has been removed

  /**
   * Simple function to compile data from scratch
   * @param {Array} inputData - Array of box data objects
   * @returns {Object} - Compiled data organized by box type
   */
  function compileDataByYNew(inputData) {
    // Create a result object with empty arrays for each box type
    const result = createEmptyDataArrays(1); // Start with 1 row, we'll expand as needed

    inputData.forEach(box => {
      result[box.type+"Arr"] = box.textRows;
    });

    return result;
  }

  // Original function now calls the new implementation
  function compileDataByY(inputData) {
    // Use the new implementation
    return compileDataByYNew(inputData);
  }

  // Handle rotation
  const handleRotateClockwise = () => {
    const newRotation = (rotation + 90) % 360;
    setRotation(newRotation);
  };

  const handleRotateCounterClockwise = () => {
    const newRotation = (rotation - 90 + 360) % 360;
    setRotation(newRotation);
  };

  const exportToCSV = () => {
    // Function to escape CSV values (wrap in quotes if contains comma or quotes)
    const escapeCSV = (value) => {
      if (!value) return '';
      // Replace double quotes with two double quotes (CSV escaping standard)
      const escaped = value.replace(/"/g, '""');
      // If value contains comma, newline, or quotes, wrap in quotes
      return escaped.includes(',') ||
        escaped.includes('"') ||
        escaped.includes('\n')
        ? `"${escaped}"`
        : escaped;
    };

    // Add headers row using our configuration
    const headers =
      'Page,' +
      Object.values(BOX_TYPES)
        .map((type) => type.label)
        .join(',') +
      '\n';

    // Collect rows from all pages
    let allRows = [];

    // First add current page data if it exists and isn't in allExtractedData
    if (extractedData?.descriptionArr?.length > 0) {
      const currentPageKey = `page${pageNumber}`;
      const isCurrentPageInAll =
        allExtractedData[currentPageKey] &&
        JSON.stringify(allExtractedData[currentPageKey]) ===
          JSON.stringify(extractedData);

      if (!isCurrentPageInAll) {
        extractedData.descriptionArr.forEach((_, index) => {
          const row = { page: pageNumber };

          // Add data for each box type
          Object.keys(BOX_TYPES).forEach((boxType) => {
            const arrayName = `${boxType}Arr`;
            row[boxType] = extractedData[arrayName]?.[index] || '';
          });

          allRows.push(row);
        });
      }
    }

    // Then add data from all pages
    Object.entries(allExtractedData).forEach(([pageKey, data]) => {
      if (!data || !data.descriptionArr || data.descriptionArr.length === 0)
        return;

      const pageNum = pageKey.replace('page', '');

      data.descriptionArr.forEach((_, index) => {
        const row = { page: pageNum };

        // Add data for each box type
        Object.keys(BOX_TYPES).forEach((boxType) => {
          const arrayName = `${boxType}Arr`;
          row[boxType] = data[arrayName]?.[index] || '';
        });

        allRows.push(row);
      });
    });

    // Convert rows to CSV format
    const dataRows = allRows
      .map((row) => {
        // Start with the page number
        let csvRow = escapeCSV(row.page);

        // Add each box type in the same order as the headers
        Object.keys(BOX_TYPES).forEach((boxType) => {
          csvRow += ',' + escapeCSV(row[boxType] || '');
        });

        return csvRow;
      })
      .join('\n');

    const csvContent = headers + dataRows;

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'extracted_data.csv';
    a.click();
    URL.revokeObjectURL(url);

    logExtract('CSV export completed', {
      totalRows: allRows.length,
      fileSize: Math.round(csvContent.length / 1024) + ' KB',
      fileName: 'extracted_data.csv',
      timestamp: new Date().toISOString(),
    });
  };

  // Handle undo box action
  const handleUndoBox = () => {
    if (boxes.length > 0) {
      const lastBox = boxes[boxes.length - 1];

      // Remove the box
      setBoxes((prev) => prev.slice(0, -1));

      // Remove its ID from processed box IDs
      setProcessedBoxIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(lastBox.id);
        return newSet;
      });

      // Remove from processing box IDs if it's being processed
      setProcessingBoxIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(lastBox.id);
        return newSet;
      });

      // Update raw box data
      const pageKey = `page${pageNumber}`;

      // First update the raw box data
      setRawBoxData((prev) => {
        // Get current page data
        const pageData = prev[pageKey] || [];

        // Filter out this box
        const updatedPageData = pageData.filter(
          (item) => item.id !== lastBox.id
        );

        // Create updated raw box data
        const newRawBoxData = {
          ...prev,
          [pageKey]: updatedPageData,
        };

        // Update the ref with the latest data
        rawBoxDataRef.current = newRawBoxData;

        // Return updated raw box data
        return newRawBoxData;
      });

      // Now get ALL raw data for this page to compile
      setTimeout(() => {
        // Get the latest raw data from the ref
        const allPageData = rawBoxDataRef.current[pageKey] || [];

        // Compile data from ALL boxes on this page
        const compiledData = compileDataByY(allPageData);

        // Update the current page data
        setExtractedData(compiledData);

        // Update the all extracted data store
        setAllExtractedData((prev) => ({
          ...prev,
          [pageKey]: compiledData,
        }));

        logBox(`UI updated after removing box ${lastBox.id}`, {
          boxId: lastBox.id,
          boxType: lastBox.type,
          pageNumber,
          remainingBoxes: boxes.length - 1,
          timestamp: new Date().toISOString(),
        });
      }, 10); // Slightly longer timeout to ensure state updates have completed

      logBox(`Box removed from page ${pageNumber}`, {
        boxId: lastBox.id,
        boxType: lastBox.type,
        position: lastBox.rect,
        remainingBoxes: boxes.length - 1,
        timestamp: new Date().toISOString(),
      });
    }
  };

  // Update error message in parent component
  useEffect(() => {
    if (onError && errorMessage) {
      onError(errorMessage);
    }
  }, [errorMessage, onError]);

  // Utility functions for viewport detection
  const getVisiblePages = () => Array.from(visiblePages).sort((a, b) => a - b);
  const getFullyVisiblePages = () => Array.from(fullyVisiblePages).sort((a, b) => a - b);
  const isPageVisible = (pageIndex) => visiblePages.has(pageIndex);
  const isPageFullyVisible = (pageIndex) => fullyVisiblePages.has(pageIndex);
  const getVisibilityInfo = () => ({
    visiblePages: getVisiblePages(),
    fullyVisiblePages: getFullyVisiblePages(),
    totalPages: pdfPages.length,
    visibleCount: visiblePages.size,
    fullyVisibleCount: fullyVisiblePages.size
  });

  // Expose methods to parent component via ref
  React.useImperativeHandle(ref, () => ({
    handleUndoBox,
    extractImages,
    exportCSV,
    setCurrentBoxType: (type) => setCurrentBoxType(type),
    // Viewport detection methods
    getVisiblePages,
    getFullyVisiblePages,
    isPageVisible,
    isPageFullyVisible,
    getVisibilityInfo,

    // Methods for AWS Textract integration
    getBoxes: () => boxes,
    getPageNumber: () => pageNumber,
    getCanvasWidth: () => canvasRef.current?.width || 0,
    getCanvasHeight: () => canvasRef.current?.height || 0,
    updateExtractedData: (textractExtractedData) => {
      // Convert Textract extracted data to our format
      const pageKey = `page${pageNumber}`;

      // Initialize arrays for each box type
      const newPageData = {};
      Object.keys(BOX_TYPES).forEach(type => {
        newPageData[`${type}Arr`] = [];
        newPageData[`${type}Ids`] = [];
      });

      // Add data from each box
      Object.entries(textractExtractedData).forEach(([boxId, data]) => {
        const boxType = data.type;

        // Add to the appropriate arrays
        if (boxType && BOX_TYPES[boxType]) {
          newPageData[`${boxType}Arr`].push(data.text || '');
          newPageData[`${boxType}Ids`].push(boxId);

          // Mark this box as processed
          setProcessedBoxIds(prev => {
            const newSet = new Set(prev);
            newSet.add(boxId);
            return newSet;
          });
        }
      });

      // Update extracted data for this page
      setExtractedData(prev => ({
        ...prev,
        [pageKey]: newPageData
      }));

      // Update all extracted data
      setAllExtractedData(prev => ({
        ...prev,
        [pageKey]: newPageData
      }));

      // Notify parent that we have extracted data
      // if (onExtractedDataChange) {
      //   onExtractedDataChange(true);
      // }
    },
    // Method to receive and initialize TextractRBush with Textract data
    initializeTextractRBush: (textractData) => {
      if (!textractData || !textractData.Blocks) {
        return null;
      }
      if(!isImageBasedPdf){
        return null;
      }

      try {
        // Create a new TextractRBush instance
        const textractRBushInstance = TextractRBush();

        // Initialize with Textract data
        textractRBushInstance.initialize(textractData);

        // Store the instance in state
        setTextractRBush(textractRBushInstance);
        setTextractRBushInitialized(true);

        return textractRBushInstance;
      } catch (error) {
        console.error('Failed to initialize TextractRBush', error);
        return null;
      }
    }
  }));

  useEffect(()=>{
    if(onExtractedDataChange && displayArrays && Object.keys(displayArrays).length > 0 && Object.keys(displayArrays[Object.keys(displayArrays)[0]]).length > 0)  {
      onExtractedDataChange(true);
    }else{
      onExtractedDataChange(false);
    }
  },[displayArrays])

  // Effect to handle when TextractRBush becomes available
  // useEffect(() => {
  //   if (textractRBush) {
  //     // Process boxes if requested
  //     if (processWhenReady && boxes.length > 0) {
  //       const currentPageKey = `page${pageNumber}`;
  //       const tempBoxes = {};
  //       tempBoxes[currentPageKey] = boxes;

  //       logBox(`Saving boxes for page ${pageNumber}`, {
  //         pageNumber,
  //         boxCount: boxes.length,
  //         boxTypes: boxes.reduce((acc, box) => {
  //           acc[box.type] = (acc[box.type] || 0) + 1;
  //           return acc;
  //         }, {}),
  //       });

  //       const all_boxes = {...allBoxes, ...tempBoxes };
  //       setAllBoxes((prev) => {
  //         return {...prev, ...tempBoxes };
  //       });
  //       processAllBoxes(all_boxes);

  //       setProcessWhenReady(false);
  //     }
  //   }
  // }, [textractRBush, processWhenReady, boxes, pageNumber, allBoxes, processAllBoxes, logBox]);


  // Add keyboard event listener to close magnifying glass with Esc key
  React.useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && showMagnifyingGlass) {
        setShowMagnifyingGlass(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [showMagnifyingGlass]);

  useEffect(() => {
    if(updatedScrollViewPort && containerRef.current && scrollableContainerRef.current){
      setScrollViewPort(updatedScrollViewPort);
      const {width, height} = containerRef.current.getBoundingClientRect();
      scrollableContainerRef.current.scrollLeft = updatedScrollViewPort.x * width;
      scrollableContainerRef.current.scrollTop = updatedScrollViewPort.y * height;
    }
  }, [updatedScrollViewPort]);

  const edgeThreshold = 60;
  const autoScrollSpeed = 10;

  const [vScrollSpeed, setVScrollSpeed] = useState(0);
  const vScrollSpeedRef = useRef(0);
  vScrollSpeedRef.current = vScrollSpeed;
  const [hScrollSpeed, setHScrollSpeed] = useState(0);
  const hScrollSpeedRef = useRef(0);
  hScrollSpeedRef.current = hScrollSpeed;

  const handleMouseOut = (event) => {
    setHScrollSpeed(0);
    setVScrollSpeed(0);
    setdoAutoScroll(false);
  };

  const handleMouseMove = (event) => {
    if(!doAutoScroll){
      setHScrollSpeed(0);
      setVScrollSpeed(0);
      return;
    }
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    if(x < edgeThreshold){
      setHScrollSpeed(autoScrollSpeed*(x - edgeThreshold)/edgeThreshold);
    }else if(x > rect.width - edgeThreshold){
      setHScrollSpeed(autoScrollSpeed*(x - rect.width + edgeThreshold)/edgeThreshold);
    }else{
      setHScrollSpeed(0);
    }

    if(y < edgeThreshold){
      setVScrollSpeed(autoScrollSpeed*(y - edgeThreshold)/edgeThreshold);
    }else if(y > rect.height - edgeThreshold){
      setVScrollSpeed(autoScrollSpeed*(y - rect.height + edgeThreshold)/edgeThreshold);
    }else{
      setVScrollSpeed(0);
    }
  };

  // Add these useEffect hooks to update the refs when state changes
  useEffect(() => {
    vScrollSpeedRef.current = vScrollSpeed;
  }, [vScrollSpeed]);

  useEffect(() => {
    hScrollSpeedRef.current = hScrollSpeed;
  }, [hScrollSpeed]);

  const rafIdRef = useRef(null);
  const animateScroll = () => {
    rafIdRef.current = requestAnimationFrame(animateScroll);
    // Check if the container exists
    if (!scrollableContainerRef.current) {
      console.warn('scrollableContainerRef.current is null');
      return;
    }
    
    // Apply scrolling if speeds are non-zero
    if (hScrollSpeedRef.current) {
      scrollableContainerRef.current.scrollLeft += hScrollSpeedRef.current;
    }
    
    if (vScrollSpeedRef.current) {
      scrollableContainerRef.current.scrollTop += vScrollSpeedRef.current;
    }
  };

  // Start animation frame after component is mounted
  useEffect(() => {
    // Wait a small delay to ensure the ref is attached
    const timeoutId = setTimeout(() => {
      if (scrollableContainerRef.current) {
        console.log('Starting animation frame with valid container');
        rafIdRef.current = requestAnimationFrame(animateScroll);
      } else {
        console.warn('Container ref not available after timeout');
      }
    }, 100);
    
    return () => {
      clearTimeout(timeoutId);
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }
    };
  }, []);

  const handleScroll = (e) => {
    if(!containerRef.current || !scrollableContainerRef.current) return;
    const containerRect = containerRef.current.getBoundingClientRect();
    const calculatedScrollViewPort = {
      x: scrollableContainerRef.current.scrollLeft/containerRect.width,
      y: scrollableContainerRef.current.scrollTop/containerRect.height,
      width: scrollableContainerRef.current.clientWidth/containerRect.width,
      height: scrollableContainerRef.current.clientHeight/containerRect.height,
    }
    setScrollViewPort(calculatedScrollViewPort);
  };

  useEffect(() => {
    if (!containerRef.current || !scrollableContainerRef.current) return;
    const observer = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        const calculatedScrollViewPort = {
          x: scrollableContainerRef.current.scrollLeft/width,
          y: scrollableContainerRef.current.scrollTop/height,
          width: scrollableContainerRef.current.clientWidth/width,
          height: scrollableContainerRef.current.clientHeight/height,
        }
        setScrollViewPort(calculatedScrollViewPort);
      }
    });
    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, []);

  return (
    <div 
      style={{width:'100%', height:'100%'}}
    >
      {/* Toolbar is now handled by the parent component */}

      {errorMessage && <p className={clsx(styles.alert, styles.alertError)}>{errorMessage}</p>}

      <div className={styles.scrollableContainer}
       ref={scrollableContainerRef}
       onScroll={handleScroll}
       onMouseMove={handleMouseMove}
       onMouseOut={handleMouseOut}
       >
        <div ref={containerRef} style={{width:'max-content', margin:'0 auto'}}>
      { (numPages && pdfPages?.length > 0) ?
      pdfPages.map((page, index )=>{
        return (
          <div
            className={styles.pdfPageContainer}
            key={index}
            data-page-index={index}
          >
            <PdfPage
              key={index}
              index={index}
              ref={el => {
                pdfPageRefs.current[index] = el}
              }
              onMount={onPdfPageMounted}
              page={page}
              scale={scale}
              fineRotations={0}
              parentShowMagnifyingGlass={showMagnifyingGlass}
              snapToGrid={snapToGrid}
              gridOpacity={gridOpacity}
              autoSelectColumns={autoDetectColumns}
              textractRBush={textractRBush}
              overlapPercent={overlapPercent}
            />
          </div>);
      }):<></>
      }
      </div>
      </div>
    </div>
  );
});

export default PdfTextExtractorOCR;
