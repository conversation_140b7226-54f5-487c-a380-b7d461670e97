// @ts-nocheck
import { useNavigate, useLocation } from 'react-router';
import styles from './onboardingThankyou.module.scss';
import { routes } from '../../common';
import OnboardingFooter from './onboardingFooter';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { getChannelWindow } from '@bryzos/giss-ui-library';
import SearchHeader from '../SearchHeader';

function OnboardingThankYou() {
  const channelWindow = getChannelWindow()  ;
  const isNotificationEnabled = channelWindow?.openNotificationSetting ? window.electron.sendSync({ channel: channelWindow.openNotificationSetting, data: null}) : false;
  const [notificationEnabled, setNotificationEnabled] = useState(isNotificationEnabled);
    const navigate = useNavigate();
    const handleCheckboxChange = () => {
      if(channelWindow?.openNotificationSetting)
        window.electron.send({ channel: channelWindow.openNotificationSetting, data: !notificationEnabled})
      setNotificationEnabled(!notificationEnabled);
    };
    const location = useLocation();
    // const { isUserApproved } = location.state;
    
    return (
        <div className={styles.thankyouBox}>
          <div>
            <SearchHeader />
          </div>
            {/* <div className={styles.onboardingLogo}>
                <img src='/onboardingLogo.png' />
            </div> */}
            <div className={styles.thankyouBoxContent}>
                <h2 className={styles.title}>Thank you for joining!</h2>
                {/* {!isUserApproved ?  */}
                <>
                  <p className={styles.thnkPara}>Your information is currently being reviewed. You will receive
                    an email at the provided email address with your login
                    credentials once review is complete.</p>
                </>
                {/* : 
                <p className={styles.thnkPara}>You are now approved and can log in using the credentials you created during signup.</p> 
                } */}
                {/* {!notificationEnabled &&
                  <div className='turnOnNotif'>
                    <label className='containerChk'><input type='checkbox'  checked={notificationEnabled} onChange={handleCheckboxChange}/><span className='checkmark'></span>Click here to enable notifications</label>
                  </div>
                } */}
            </div>
            <div className={styles.btnFooterLogin}>
            <button className={styles.loginBtn} onClick={() => navigate(routes.loginPage)}><span>Login</span></button>
            </div>
        </div>
    );
}
export default OnboardingThankYou;