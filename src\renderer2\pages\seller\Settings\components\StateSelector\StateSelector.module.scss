.container {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 8px;
    padding: 16px;
    background-color: #2a2a2e;
    border-radius: 8px;
    width: fit-content;
  }
  
  .stateItem {
    padding: 8px 4px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    color: #ffffff;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s ease-in-out;
    border: 1px solid transparent;
    background: transparent;

    &:focus{
        background-color: #3e3e44;
    }

  }
  
  .stateItem:hover {
    background-color: #3e3e44;
  }
  
  .selected {
    background-color: #007aff !important;
    color: #ffffff !important;
  }
  
  .selected:hover {
    background-color: #0056b3;
  }
  