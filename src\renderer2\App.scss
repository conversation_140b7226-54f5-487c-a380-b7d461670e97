.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.widgetCanvas{
  display: inline-flex;
  position: relative;
  background: transparent;
  border-radius: 16px;
  // width: 100%;
  overflow: hidden;
  // max-width: 800px;
}
.webBackground{
  background-color: #000;
}
.webLoginHeight{
  // height: 108px;
}

.loaderContent{
  border-radius: 0px 0px 10px 10px;
  // -webkit-backdrop-filter: blur(60px);
  // backdrop-filter: blur(60px);
  // background-color: rgba(0, 0, 0, 0.75);
  margin: 0px auto;
  max-width: 800px;
  position: absolute;
  inset: 0;
  z-index: 10;
}

.bgImg {
  display: inline-flex;
  background-color: transparent;
  height: auto;
  left: 0px;
  mix-blend-mode: normal;
  object-fit: cover;
  top: 0px;
  // width: 100%;
  // z-index: 99;
  // background-image: url('https://prod-bryzos-assets.imgix.net/img/bg-app.png?w=700&h=900&auto=compress');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  // max-width:800px;
  // margin: 0 auto;
  position: relative;
  // gap: 30px;
}
.routerContainer{
  width: 800px;
  display: flex;
  justify-content: end;
}
.routerContent {
  width: 800px;
  background-color: #0f0f14;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  left: 0px;
  transition: left var(--animation-duration) linear;
}

.loaderRunning{
  opacity: 0 !important;
}

.blurBg{
  border-radius: 0px;
  background-color: rgba(0, 0, 0, 0.5);
}

.appBg {
  border-radius: 27.3px;
  background-repeat: no-repeat;
  background-size: contain;
}

.bgEllips {
  position: absolute;
  z-index: 1;
  top: -37px;
  left: 25px;
  width: 225px;
  height: 55px;
  filter: blur(55px);
  background-image: conic-gradient(from 0.32turn, var(--W--01), rgba(151, 134, 255, 0.59) 0.99turn, var(--W--01) 0.09turn, rgba(151, 134, 255, 0.1) 0.53turn, var(--W--01) 0.8turn, rgba(151, 134, 255, 0) 0.31turn, var(--W--01)), linear-gradient(348deg, var(--neutral-light) 210%, var(--neutral-light-transparent) 165%), linear-gradient(to bottom, rgba(151, 134, 255, 0), var(--secondary-color));
}

.bgEllips1 {
  position: absolute;
  z-index: 1;
  bottom: -26px;
  right: 23px;
  width: 78px;
  height: 32.5px;
  filter: blur(26px);
  background-image: conic-gradient(from 0.42turn, var(--W--01), rgba(151, 134, 255, 0.37) 0.89turn, var(--W--01) 0.09turn, rgba(151, 134, 255, 0.02) 0.83turn, var(--W--01) 0.51turn, var(--tertiary-color) 0.36turn, var(--W--01)), linear-gradient(274deg, var(--neutral-light) 210%, var(--neutral-light-transparent) 165%), linear-gradient(to bottom, rgba(151, 134, 255, 0.19), var(--secondary-color));
}

.orderSummaryContainer{
  .bgEllips{
    width: 170px;
    height: 45px;
  }
}


.toggle-switch {
  position: absolute;
  display: inline-block;
  width: 50px;
  height: 25px;
  top: 12px;
  right: 25px;
  z-index: 99;
}

.toggle-switch input {
  display: none;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 25px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 21px;
  width: 21px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:checked + .slider:before {
  transform: translateX(25px);
}
.WrapperContent{
  display: flex;
  flex-direction: row;
  // gap: 30px;
  padding: 24px;
  position: relative;
}
.RightContent{
  width: auto;
  display: flex;
  flex-direction: column;
}

.wrapperOverlay{
  position: absolute;
  inset: -24px;
  background: url('./assets/New-images/Bg-App/imgBg1.png') no-repeat;
  background-size: cover;
  border-radius: 16px;
}
.wrapperOverlay1{
  position: absolute;
  inset: -24px;
  background: url('./assets/New-images/Bg-App/imgBg3.png') no-repeat;
  background-size: cover;
  border-radius: 16px;
}
.wrapperOverlay2{
  position: absolute;
  inset: -24px;
  background: url('./assets/New-images/Bg-App/imgBg2.png') no-repeat;
  background-size: cover;
  border-radius: 16px;
}
.acrylicCorner{
  position: relative;
  top:27px;
  opacity: 0.5;
}

.increaseWidth{
  width: 1151px;
}
.widthAnimation{
  transition: width var(--animation-duration) linear;
}
.moveRight{
  left: 351px;
}

.closeAndMinimizeBtn {
  display: flex;
  flex-direction: row-reverse;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 8px;
  right: 0px;
  z-index: 999999;
}
.macCloseAndMinimizeBtn{
  left: 5px;
  right: unset;
  flex-direction: row;
}

.hideCloseAndMinimizeBtn{
  display: none;
}

.wrapperMenuBorder{
  border-bottom-left-radius: 0px;
  border-top-left-radius: 0px;
}

.fillBorderRightBottom{
  border-bottom-right-radius: 0px;
}

.fillBorderRightTop{
  border-top-right-radius: 0px;
}
.positionCloseAndMinimizeBtn{
  left: 356px;
}

.referenceDataLoader {
  display: flex;
  column-gap: 16px;
  z-index: 9999;

  .fetchDataLoader {
    font-family: Inter;
    font-size: 16px;
    color: #fff;
    min-width: 200px;
    position: relative;

    &::after {
      content: '...'; // fixed content
      display: inline-block;
      width: 1.5em; // enough space for 3 dots
      text-align: left;
      margin-left: 2px;
      animation: stableDots 1s steps(4, end) infinite;
    }
  }

  @keyframes stableDots {
    0%   { content: '.'; }
    25%  { content: '..'; }
    50%  { content: '...'; }
    75%  { content: ''; }
    100% { content: '.'; }
  }
}
